const { MongoClient } = require('mongodb');
require('dotenv').config();

class EnhancedDataVerifier {
    constructor() {
        this.mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
        this.dbName = process.env.DATABASE_NAME || 'referral_bot';
        this.client = null;
        this.db = null;

        console.log('🔧 Using MongoDB URI:', this.mongoUri ? 'Set from environment' : 'Using default localhost');
        console.log('🔧 Using Database Name:', this.dbName);
    }

    async connect() {
        try {
            this.client = new MongoClient(this.mongoUri);
            await this.client.connect();
            this.db = this.client.db(this.dbName);
            console.log('✅ Connected to MongoDB for verification');
            return true;
        } catch (error) {
            console.error('❌ MongoDB connection failed:', error.message);
            return false;
        }
    }

    async disconnect() {
        if (this.client) {
            await this.client.close();
            console.log('🔌 Disconnected from MongoDB');
        }
    }

    async verifyUserData(userId) {
        console.log(`\n🔍 VERIFYING USER: ${userId}`);
        console.log('=' .repeat(60));

        try {
            // Get raw database data
            const dbUser = await this.db.collection('users').findOne({ user_id: parseInt(userId) });
            if (!dbUser) {
                console.log('❌ User not found in database');
                return false;
            }

            // Get API response
            const apiResponse = await fetch(`http://localhost:3001/api/stats/${userId}`);
            const apiStats = await apiResponse.json();

            const userApiResponse = await fetch(`http://localhost:3001/api/user/${userId}`);
            const apiUser = await userApiResponse.json();

            // Verify basic user data
            await this.verifyBasicUserData(dbUser, apiUser);

            // Verify financial calculations
            await this.verifyFinancialCalculations(dbUser, apiStats);

            // Verify today's earnings
            await this.verifyTodayEarnings(dbUser, apiStats, userId);

            // Verify withdrawal history
            await this.verifyWithdrawalHistory(dbUser, apiStats, userId);

            // Verify gift code history
            await this.verifyGiftCodeHistory(apiStats, userId);

            // Verify custom referral links
            await this.verifyCustomReferralLinks(apiStats, userId);

            // Verify daily earnings breakdown
            await this.verifyDailyEarningsBreakdown(dbUser, apiStats, userId);

            console.log('✅ User verification completed');
            return true;

        } catch (error) {
            console.error('❌ Verification failed:', error.message);
            return false;
        }
    }

    async verifyBasicUserData(dbUser, apiUser) {
        console.log('\n📋 BASIC USER DATA VERIFICATION:');
        
        const checks = [
            { field: 'user_id', db: dbUser.user_id, api: apiUser.user_id },
            { field: 'first_name', db: dbUser.first_name, api: apiUser.first_name },
            { field: 'username', db: dbUser.username, api: apiUser.username },
            { field: 'balance', db: dbUser.balance, api: apiUser.balance },
            { field: 'successful_withdraw', db: dbUser.successful_withdraw, api: apiUser.successful_withdraw },
            { field: 'banned', db: dbUser.banned, api: apiUser.banned },
            { field: 'referred_by', db: dbUser.referred_by, api: apiUser.referred_by },
            { field: 'created_at', db: dbUser.created_at, api: apiUser.created_at }
        ];

        checks.forEach(check => {
            const match = check.db === check.api;
            console.log(`${match ? '✅' : '❌'} ${check.field}: DB=${check.db} | API=${check.api}`);
        });
    }

    async verifyFinancialCalculations(dbUser, apiStats) {
        console.log('\n💰 FINANCIAL CALCULATIONS VERIFICATION:');

        // Verify referral earnings calculation
        const promotionReport = dbUser.promotion_report || [];
        const calculatedEarnings = promotionReport.reduce((sum, report) => sum + (report.amount_got || 0), 0);
        
        console.log(`✅ Promotion Report Entries: ${promotionReport.length}`);
        console.log(`${calculatedEarnings === apiStats.totalEarnings ? '✅' : '❌'} Referral Earnings: DB_Calculated=${calculatedEarnings} | API=${apiStats.totalEarnings}`);

        // Verify balance
        console.log(`${dbUser.balance === apiStats.userBalance ? '✅' : '❌'} Current Balance: DB=${dbUser.balance} | API=${apiStats.userBalance}`);

        // Verify net earnings calculation
        const calculatedNetEarnings = (dbUser.balance || 0) + (dbUser.successful_withdraw || 0);
        console.log(`✅ Net Earnings Calculation: Balance(${dbUser.balance}) + Withdrawals(${dbUser.successful_withdraw}) = ${calculatedNetEarnings}`);

        // Verify referral counts
        const directReferrals = await this.db.collection('users').countDocuments({
            referred_by: dbUser.user_id.toString()
        });
        
        const activeReferrals = await this.db.collection('users').countDocuments({
            referred_by: dbUser.user_id.toString(),
            banned: { $ne: true }
        });

        console.log(`${directReferrals === apiStats.totalReferrals ? '✅' : '❌'} Total Referrals: DB=${directReferrals} | API=${apiStats.totalReferrals}`);
        console.log(`${activeReferrals === apiStats.activeReferrals ? '✅' : '❌'} Active Referrals: DB=${activeReferrals} | API=${apiStats.activeReferrals}`);
    }

    async verifyTodayEarnings(dbUser, apiStats, userId) {
        console.log('\n📅 TODAY\'S EARNINGS VERIFICATION:');

        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const startOfDayTimestamp = Math.floor(startOfDay.getTime() / 1000);

        // Get today's referrals from database
        const todayReferrals = await this.db.collection('users').find({
            referred_by: userId.toString(),
            created_at: { $gte: startOfDayTimestamp }
        }).toArray();

        // Calculate expected today's earnings
        const promotionReport = dbUser.promotion_report || [];
        let expectedTodayEarnings = 0;
        
        todayReferrals.forEach(referral => {
            const report = promotionReport.find(p => p.referred_user_id === referral.user_id);
            if (report) {
                expectedTodayEarnings += report.amount_got || 0;
            }
        });

        console.log(`${expectedTodayEarnings === apiStats.todayEarnings.amount ? '✅' : '❌'} Today's Earnings: DB_Calculated=${expectedTodayEarnings} | API=${apiStats.todayEarnings.amount}`);
        console.log(`${todayReferrals.length === apiStats.todayEarnings.referrals ? '✅' : '❌'} Today's Referrals: DB=${todayReferrals.length} | API=${apiStats.todayEarnings.referrals}`);
        
        const expectedDate = today.toISOString().split('T')[0];
        console.log(`${expectedDate === apiStats.todayEarnings.date ? '✅' : '❌'} Today's Date: Expected=${expectedDate} | API=${apiStats.todayEarnings.date}`);
    }

    async verifyWithdrawalHistory(dbUser, apiStats, userId) {
        console.log('\n🏦 WITHDRAWAL HISTORY VERIFICATION:');

        // Get withdrawals from database
        const withdrawalsFromDB = await this.db.collection('withdrawals').find({
            user_id: parseInt(userId)
        }).sort({ created_at: -1 }).limit(20).toArray();

        // Get user's withdrawal reports
        const userWithdrawals = dbUser.withdrawal_reports || [];

        console.log(`✅ Database Withdrawals Found: ${withdrawalsFromDB.length}`);
        console.log(`✅ User Withdrawal Reports: ${userWithdrawals.length}`);
        console.log(`✅ API Withdrawal History: ${apiStats.withdrawalHistory.length}`);

        // Verify some withdrawal data if available
        if (withdrawalsFromDB.length > 0) {
            const firstWithdrawal = withdrawalsFromDB[0];
            const apiFirstWithdrawal = apiStats.withdrawalHistory.find(w => w.source === 'database');
            
            if (apiFirstWithdrawal) {
                console.log(`${firstWithdrawal.amount === apiFirstWithdrawal.amount ? '✅' : '❌'} First Withdrawal Amount: DB=${firstWithdrawal.amount} | API=${apiFirstWithdrawal.amount}`);
                console.log(`${firstWithdrawal.status === apiFirstWithdrawal.status ? '✅' : '❌'} First Withdrawal Status: DB=${firstWithdrawal.status} | API=${apiFirstWithdrawal.status}`);
            }
        }
    }

    async verifyGiftCodeHistory(apiStats, userId) {
        console.log('\n🎁 GIFT CODE HISTORY VERIFICATION:');

        // Check gift codes collection
        const giftCodes = await this.db.collection('gift_codes').find({
            redeemed_by: parseInt(userId)
        }).toArray();

        // Check link-based codes collection
        const linkCodes = await this.db.collection('link_based_codes').find({
            claimed_users: parseInt(userId)
        }).toArray();

        const totalExpectedCodes = giftCodes.length + linkCodes.length;
        
        console.log(`✅ Gift Codes in DB: ${giftCodes.length}`);
        console.log(`✅ Link Codes in DB: ${linkCodes.length}`);
        console.log(`${totalExpectedCodes === apiStats.giftCodeHistory.length ? '✅' : '❌'} Total Gift Codes: DB=${totalExpectedCodes} | API=${apiStats.giftCodeHistory.length}`);
    }

    async verifyCustomReferralLinks(apiStats, userId) {
        console.log('\n🔗 CUSTOM REFERRAL LINKS VERIFICATION:');

        const customLinks = await this.db.collection('custom_referrals').find({
            user_id: parseInt(userId)
        }).toArray();

        console.log(`${customLinks.length === apiStats.customReferralLinks.length ? '✅' : '❌'} Custom Links: DB=${customLinks.length} | API=${apiStats.customReferralLinks.length}`);

        if (customLinks.length > 0 && apiStats.customReferralLinks.length > 0) {
            const dbFirst = customLinks[0];
            const apiFirst = apiStats.customReferralLinks[0];
            console.log(`${dbFirst.custom_param === apiFirst.parameter ? '✅' : '❌'} First Link Parameter: DB=${dbFirst.custom_param} | API=${apiFirst.parameter}`);
        }
    }

    async verifyDailyEarningsBreakdown(dbUser, apiStats, userId) {
        console.log('\n📊 DAILY EARNINGS BREAKDOWN VERIFICATION:');

        // Get all referrals for this user
        const allReferrals = await this.db.collection('users').find({
            referred_by: userId.toString()
        }).toArray();

        // Calculate daily breakdown manually
        const promotionReport = dbUser.promotion_report || [];
        const dailyBreakdown = {};

        allReferrals.forEach(referral => {
            if (referral.created_at) {
                const date = new Date(referral.created_at * 1000).toISOString().split('T')[0];
                
                if (!dailyBreakdown[date]) {
                    dailyBreakdown[date] = { date, referrals: 0, earnings: 0 };
                }
                
                dailyBreakdown[date].referrals++;
                
                const report = promotionReport.find(p => p.referred_user_id === referral.user_id);
                if (report) {
                    dailyBreakdown[date].earnings += report.amount_got || 0;
                }
            }
        });

        const calculatedDays = Object.values(dailyBreakdown).length;
        console.log(`✅ Calculated Daily Entries: ${calculatedDays}`);
        console.log(`✅ API Daily Entries: ${apiStats.dailyEarnings.length}`);

        // Verify a few specific days if available
        if (apiStats.dailyEarnings.length > 0) {
            const apiFirstDay = apiStats.dailyEarnings[0];
            const calculatedFirstDay = dailyBreakdown[apiFirstDay.date];
            
            if (calculatedFirstDay) {
                console.log(`${calculatedFirstDay.referrals === apiFirstDay.referrals ? '✅' : '❌'} First Day Referrals: Calculated=${calculatedFirstDay.referrals} | API=${apiFirstDay.referrals}`);
                console.log(`${calculatedFirstDay.earnings === apiFirstDay.earnings ? '✅' : '❌'} First Day Earnings: Calculated=${calculatedFirstDay.earnings} | API=${apiFirstDay.earnings}`);
            }
        }
    }
}

// Alternative verification using API endpoints
async function runAPIBasedVerification() {
    console.log('🚀 STARTING API-BASED DATA ACCURACY VERIFICATION');
    console.log('=' .repeat(80));

    // Test users with different data profiles
    const testUsers = [
        { id: '2027123358', name: 'Kêviñ (Rich data)' },
        { id: '8153676253', name: 'Titanium Bots (Moderate data)' },
        { id: '461991991', name: 'Aneesh (Simple data)' }
    ];

    let allPassed = true;

    for (const user of testUsers) {
        console.log(`\n🔍 VERIFYING USER: ${user.id} - ${user.name}`);
        console.log('=' .repeat(60));

        try {
            // Get API responses
            const [userResponse, statsResponse] = await Promise.all([
                fetch(`http://localhost:3001/api/user/${user.id}`),
                fetch(`http://localhost:3001/api/stats/${user.id}`)
            ]);

            if (!userResponse.ok || !statsResponse.ok) {
                console.log('❌ Failed to fetch API data');
                allPassed = false;
                continue;
            }

            const userData = await userResponse.json();
            const statsData = await statsResponse.json();

            // Verify financial calculations
            await verifyFinancialConsistency(userData, statsData);

            // Verify data completeness
            await verifyDataCompleteness(userData, statsData);

            // Verify today's calculations
            await verifyTodayCalculations(statsData);

            // Verify transaction data
            await verifyTransactionData(statsData);

        } catch (error) {
            console.error('❌ Verification failed:', error.message);
            allPassed = false;
        }
    }

    console.log('\n' + '=' .repeat(80));
    console.log(`🎯 VERIFICATION SUMMARY: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    console.log('=' .repeat(80));
}

async function verifyFinancialConsistency(userData, statsData) {
    console.log('\n💰 FINANCIAL CONSISTENCY VERIFICATION:');

    // Check if promotion report earnings match total earnings
    const promotionReport = userData.promotion_report || [];
    const calculatedEarnings = promotionReport.reduce((sum, report) => sum + (report.amount_got || 0), 0);

    console.log(`${calculatedEarnings === statsData.totalEarnings ? '✅' : '❌'} Referral Earnings: Calculated=${calculatedEarnings} | API=${statsData.totalEarnings}`);
    console.log(`${userData.balance === statsData.userBalance ? '✅' : '❌'} Balance Consistency: User=${userData.balance} | Stats=${statsData.userBalance}`);

    // Verify net earnings calculation
    const expectedNetEarnings = (userData.balance || 0) + (userData.successful_withdraw || 0);
    console.log(`✅ Net Earnings Formula: Balance(${userData.balance}) + Withdrawals(${userData.successful_withdraw}) = ${expectedNetEarnings}`);

    // Check promotion report count vs API count
    console.log(`${promotionReport.length === statsData.promotionReports ? '✅' : '❌'} Promotion Reports: User=${promotionReport.length} | Stats=${statsData.promotionReports}`);
}

async function verifyDataCompleteness(userData, statsData) {
    console.log('\n📋 DATA COMPLETENESS VERIFICATION:');

    const requiredUserFields = ['user_id', 'first_name', 'balance', 'created_at'];
    const requiredStatsFields = ['totalReferrals', 'totalEarnings', 'activeReferrals', 'userBalance'];

    requiredUserFields.forEach(field => {
        const hasField = userData.hasOwnProperty(field) && userData[field] !== undefined;
        console.log(`${hasField ? '✅' : '❌'} User Field '${field}': ${hasField ? 'Present' : 'Missing'}`);
    });

    requiredStatsFields.forEach(field => {
        const hasField = statsData.hasOwnProperty(field) && statsData[field] !== undefined;
        console.log(`${hasField ? '✅' : '❌'} Stats Field '${field}': ${hasField ? 'Present' : 'Missing'}`);
    });

    // Check enhanced fields
    const enhancedFields = ['todayEarnings', 'withdrawalHistory', 'giftCodeHistory', 'customReferralLinks', 'dailyEarnings'];
    enhancedFields.forEach(field => {
        const hasField = statsData.hasOwnProperty(field);
        console.log(`${hasField ? '✅' : '❌'} Enhanced Field '${field}': ${hasField ? 'Present' : 'Missing'}`);
    });
}

async function verifyTodayCalculations(statsData) {
    console.log('\n📅 TODAY\'S CALCULATIONS VERIFICATION:');

    const todayEarnings = statsData.todayEarnings;
    if (todayEarnings) {
        const today = new Date().toISOString().split('T')[0];
        console.log(`${todayEarnings.date === today ? '✅' : '❌'} Today's Date: Expected=${today} | API=${todayEarnings.date}`);
        console.log(`${typeof todayEarnings.amount === 'number' ? '✅' : '❌'} Today's Amount Type: ${typeof todayEarnings.amount}`);
        console.log(`${typeof todayEarnings.referrals === 'number' ? '✅' : '❌'} Today's Referrals Type: ${typeof todayEarnings.referrals}`);
        console.log(`✅ Today's Values: Amount=${todayEarnings.amount}, Referrals=${todayEarnings.referrals}`);
    } else {
        console.log('❌ Today\'s earnings data missing');
    }
}

async function verifyTransactionData(statsData) {
    console.log('\n🏦 TRANSACTION DATA VERIFICATION:');

    // Verify withdrawal history structure
    const withdrawalHistory = statsData.withdrawalHistory || [];
    console.log(`✅ Withdrawal History Count: ${withdrawalHistory.length}`);

    if (withdrawalHistory.length > 0) {
        const firstWithdrawal = withdrawalHistory[0];
        const requiredFields = ['amount', 'status', 'date', 'source'];
        requiredFields.forEach(field => {
            const hasField = firstWithdrawal.hasOwnProperty(field);
            console.log(`${hasField ? '✅' : '❌'} Withdrawal Field '${field}': ${hasField ? 'Present' : 'Missing'}`);
        });
    }

    // Verify gift code history structure
    const giftCodeHistory = statsData.giftCodeHistory || [];
    console.log(`✅ Gift Code History Count: ${giftCodeHistory.length}`);

    // Verify custom referral links structure
    const customLinks = statsData.customReferralLinks || [];
    console.log(`✅ Custom Referral Links Count: ${customLinks.length}`);

    // Verify daily earnings structure
    const dailyEarnings = statsData.dailyEarnings || [];
    console.log(`✅ Daily Earnings Count: ${dailyEarnings.length}`);

    if (dailyEarnings.length > 0) {
        const firstDay = dailyEarnings[0];
        const requiredFields = ['date', 'referrals', 'earnings'];
        requiredFields.forEach(field => {
            const hasField = firstDay.hasOwnProperty(field);
            console.log(`${hasField ? '✅' : '❌'} Daily Earnings Field '${field}': ${hasField ? 'Present' : 'Missing'}`);
        });
    }
}

// Main verification function
async function runComprehensiveVerification() {
    // Try API-based verification first (more reliable)
    await runAPIBasedVerification();
}

// Run verification
runComprehensiveVerification().catch(console.error);
