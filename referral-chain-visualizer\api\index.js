const express = require('express');
const cors = require('cors');
const { MongoClient } = require('mongodb');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// MongoDB connection
let db;
let client;

async function connectToDatabase() {
    try {
        const mongoUri = process.env.MONGODB_URI;
        const dbName = process.env.DATABASE_NAME || 'referral_bot';
        
        if (!mongoUri) {
            throw new Error('MONGODB_URI environment variable is not set');
        }

        client = new MongoClient(mongoUri, {
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000,
            socketTimeoutMS: 20000,
        });

        await client.connect();
        db = client.db(dbName);
        
        // Test connection
        await db.command({ ping: 1 });
        console.log('Connected to MongoDB successfully');
        
        return db;
    } catch (error) {
        console.error('MongoDB connection failed:', error);
        throw error;
    }
}

// Database helper functions
class ReferralChainService {
    constructor(database) {
        this.db = database;
        this.usersCollection = database.collection('users');
        this.customReferralsCollection = database.collection('custom_referrals');

        // Cache for frequently accessed data
        this.userCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    // Cache management
    getCachedUser(userId) {
        const cached = this.userCache.get(userId);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        return null;
    }

    setCachedUser(userId, userData) {
        this.userCache.set(userId, {
            data: userData,
            timestamp: Date.now()
        });

        // Limit cache size
        if (this.userCache.size > 1000) {
            const firstKey = this.userCache.keys().next().value;
            this.userCache.delete(firstKey);
        }
    }

    async getUserById(userId) {
        try {
            // Check cache first
            const cached = this.getCachedUser(userId);
            if (cached) {
                return cached;
            }

            const user = await this.usersCollection.findOne(
                { user_id: parseInt(userId) },
                {
                    projection: {
                        _id: 0, // Exclude MongoDB _id field
                        user_id: 1,
                        first_name: 1,
                        last_name: 1,
                        username: 1,
                        referred_by: 1,
                        balance: 1,
                        banned: 1,
                        created_at: 1,
                        promotion_report: 1,
                        account_info: 1,
                        successful_withdraw: 1
                    }
                }
            );

            if (user) {
                this.setCachedUser(userId, user);
            }

            return user;
        } catch (error) {
            console.error('Error getting user by ID:', error);
            return null;
        }
    }

    async getDirectReferrals(userId, options = {}) {
        try {
            const {
                page = 1,
                limit = 50,
                includeBanned = false,
                includeCustomReferrals = true
            } = options;

            const skip = (page - 1) * limit;
            
            // Build query
            const query = { referred_by: userId.toString() };
            
            if (!includeBanned) {
                query.banned = { $ne: true };
            }

            // Get referrals with pagination
            const referrals = await this.usersCollection
                .find(query)
                .sort({ created_at: -1 })
                .skip(skip)
                .limit(limit)
                .toArray();

            // Get total count
            const totalCount = await this.usersCollection.countDocuments(query);

            // Process custom referrals if needed
            if (includeCustomReferrals) {
                for (let referral of referrals) {
                    const referredBy = referral.referred_by;
                    if (referredBy && !referredBy.match(/^\d+$/)) {
                        // It's a custom referral parameter
                        const customReferral = await this.customReferralsCollection.findOne({
                            custom_param: referredBy
                        });
                        if (customReferral) {
                            referral.custom_referral_info = {
                                parameter: referredBy,
                                created_by: customReferral.user_id
                            };
                        }
                    }
                }
            }

            return {
                referrals,
                totalCount,
                hasMore: skip + referrals.length < totalCount,
                currentPage: page,
                totalPages: Math.ceil(totalCount / limit)
            };
        } catch (error) {
            console.error('Error getting direct referrals:', error);
            return { referrals: [], totalCount: 0, hasMore: false };
        }
    }

    async buildReferralChain(userId, options = {}) {
        try {
            const {
                maxDepth = 10,
                includeBanned = false,
                includeCustomReferrals = true
            } = options;

            const rootUser = await this.getUserById(userId);
            if (!rootUser) {
                return null;
            }

            const chain = {
                user: rootUser,
                children: [],
                depth: 0,
                stats: {
                    totalReferrals: 0,
                    maxDepth: 0,
                    totalEarnings: 0,
                    activeUsers: 0
                }
            };

            await this._buildChainRecursive(chain, maxDepth, {
                includeBanned,
                includeCustomReferrals
            });

            return chain;
        } catch (error) {
            console.error('Error building referral chain:', error);
            return null;
        }
    }

    async _buildChainRecursive(node, maxDepth, options, currentDepth = 0) {
        try {
            if (currentDepth >= maxDepth) {
                return;
            }

            // Ensure node has required properties
            if (!node || !node.user || !node.user.user_id) {
                console.error('Invalid node structure in _buildChainRecursive:', node);
                return;
            }

            const userId = node.user.user_id;
            const referralsData = await this.getDirectReferrals(userId, {
                page: 1,
                limit: 100, // Get first 100 referrals
                includeBanned: options.includeBanned,
                includeCustomReferrals: options.includeCustomReferrals
            });

            node.children = [];
            node.hasMore = referralsData.hasMore;
            node.totalDirectReferrals = referralsData.totalCount;

        for (const referral of referralsData.referrals) {
            const childNode = {
                user: referral,
                children: [],
                depth: currentDepth + 1,
                // Initialize stats for child nodes
                stats: {
                    totalReferrals: 0,
                    maxDepth: 0,
                    totalEarnings: 0,
                    activeUsers: 0
                }
            };

            node.children.push(childNode);

            // Update parent node stats with null checks
            if (!node.stats) {
                node.stats = {
                    totalReferrals: 0,
                    maxDepth: 0,
                    totalEarnings: 0,
                    activeUsers: 0
                };
            }

            node.stats.totalReferrals++;
            node.stats.maxDepth = Math.max(node.stats.maxDepth, currentDepth + 1);

            // Note: Chain stats show tree structure, not actual earnings
            // For accurate earnings, use the /api/stats endpoint
            if (referral.balance) {
                node.stats.totalEarnings += referral.balance;
            }

            if (!referral.banned) {
                node.stats.activeUsers++;
            }

            // Recursively build children (limit depth to prevent infinite recursion)
            if (currentDepth < maxDepth - 1) {
                await this._buildChainRecursive(childNode, maxDepth, options, currentDepth + 1);

                // Aggregate child stats with proper null checks
                if (childNode.stats && node.stats) {
                    node.stats.totalReferrals += childNode.stats.totalReferrals || 0;
                    node.stats.maxDepth = Math.max(node.stats.maxDepth, childNode.stats.maxDepth || 0);
                    node.stats.totalEarnings += childNode.stats.totalEarnings || 0;
                    node.stats.activeUsers += childNode.stats.activeUsers || 0;
                }
            }
        }
        } catch (error) {
            console.error(`Error in _buildChainRecursive for user ${node?.user?.user_id}:`, error);
            // Initialize stats if not present to prevent further errors
            if (!node.stats) {
                node.stats = {
                    totalReferrals: 0,
                    maxDepth: 0,
                    totalEarnings: 0,
                    activeUsers: 0
                };
            }
        }
    }

    async getReferralStatistics(userId) {
        try {
            const user = await this.getUserById(userId);
            if (!user) {
                return null;
            }

            const promotionReport = user.promotion_report || [];

            // Calculate actual referral earnings from promotion_report
            const totalEarnings = promotionReport.reduce((sum, report) => sum + (report.amount_got || 0), 0);

            // Get accurate direct referrals count from database
            const directReferralsCount = await this.usersCollection.countDocuments({
                referred_by: userId.toString()
            });

            // Get active (non-banned) referrals count
            const activeReferralsCount = await this.usersCollection.countDocuments({
                referred_by: userId.toString(),
                banned: { $ne: true }
            });

            // Calculate today's earnings
            const todayEarnings = await this.getTodayEarnings(userId);

            // Get withdrawal history
            const withdrawalHistory = await this.getWithdrawalHistory(userId);

            // Get gift code redemptions
            const giftCodeHistory = await this.getGiftCodeHistory(userId);

            // Get custom referral links
            const customReferralLinks = await this.getCustomReferralLinks(userId);

            // Skip recursive tree counting for performance - use direct referrals instead
            const totalTreeReferrals = directReferralsCount;

            return {
                // Direct referrals from database (most accurate)
                totalReferrals: directReferralsCount,
                // Actual earnings from referrals (from promotion_report)
                totalEarnings,
                // Active (non-banned) referrals
                activeReferrals: activeReferralsCount,
                // Promotion reports count (should match totalEarnings calculation)
                promotionReports: promotionReport.length,
                // User's current balance
                userBalance: user.balance || 0,
                // When user joined
                joinedAt: user.created_at,
                // Total referrals in entire tree (all levels)
                totalTreeReferrals,
                // Today's earnings
                todayEarnings,
                // Withdrawal history
                withdrawalHistory,
                // Gift code redemptions
                giftCodeHistory,
                // Custom referral links
                customReferralLinks,
                // Daily earnings breakdown
                dailyEarnings: await this.getDailyEarningsBreakdown(userId)
            };
        } catch (error) {
            console.error('Error getting referral statistics:', error);
            return null;
        }
    }

    async getTotalReferralTreeCount(userId, maxDepth = 10, currentDepth = 0) {
        try {
            if (currentDepth >= maxDepth) {
                return 0;
            }

            // Get direct referrals
            const directReferrals = await this.usersCollection.find({
                referred_by: userId.toString()
            }, { projection: { user_id: 1 } }).toArray();

            let totalCount = directReferrals.length;

            // Recursively count referrals of referrals
            for (const referral of directReferrals) {
                const subCount = await this.getTotalReferralTreeCount(
                    referral.user_id,
                    maxDepth,
                    currentDepth + 1
                );
                totalCount += subCount;
            }

            return totalCount;
        } catch (error) {
            console.error('Error getting total referral tree count:', error);
            return 0;
        }
    }

    async searchInChain(userId, searchTerm, options = {}) {
        try {
            const {
                maxDepth = 5,
                includeBanned = false
            } = options;

            // Build search query
            const searchQuery = {
                $and: [
                    {
                        $or: [
                            { first_name: { $regex: searchTerm, $options: 'i' } },
                            { username: { $regex: searchTerm, $options: 'i' } },
                            { user_id: isNaN(searchTerm) ? null : parseInt(searchTerm) }
                        ].filter(q => q.user_id !== null)
                    }
                ]
            };

            if (!includeBanned) {
                searchQuery.$and.push({ banned: { $ne: true } });
            }

            // Find matching users
            const matchingUsers = await this.usersCollection
                .find(searchQuery)
                .limit(50)
                .toArray();

            // Filter users that are in the referral chain
            const chainUsers = [];
            for (const user of matchingUsers) {
                const isInChain = await this._isUserInChain(userId, user.user_id, maxDepth);
                if (isInChain) {
                    chainUsers.push({
                        ...user,
                        chainPath: isInChain.path
                    });
                }
            }

            return chainUsers;
        } catch (error) {
            console.error('Error searching in chain:', error);
            return [];
        }
    }

    async _isUserInChain(rootUserId, targetUserId, maxDepth, currentPath = []) {
        if (currentPath.length >= maxDepth) {
            return false;
        }

        if (rootUserId === targetUserId) {
            return { path: currentPath };
        }

        const directReferrals = await this.getDirectReferrals(rootUserId, { limit: 1000 });
        
        for (const referral of directReferrals.referrals) {
            if (referral.user_id === targetUserId) {
                return { path: [...currentPath, rootUserId] };
            }

            const found = await this._isUserInChain(
                referral.user_id, 
                targetUserId, 
                maxDepth, 
                [...currentPath, rootUserId]
            );
            
            if (found) {
                return found;
            }
        }

        return false;
    }

    // Helper function to clean circular references from tree structure
    cleanTreeForJSON(node) {
        if (!node) return null;

        const cleanNode = {
            user: node.user,
            children: [],
            depth: node.depth || 0,
            hasMore: node.hasMore || false,
            totalDirectReferrals: node.totalDirectReferrals || 0,
            stats: node.stats || {}
        };

        // Recursively clean children
        if (node.children && Array.isArray(node.children)) {
            cleanNode.children = node.children.map(child => this.cleanTreeForJSON(child));
        }

        return cleanNode;
    }

    async getUserReferralsWithDetails(userId, filterDate = null) {
        try {
            const user = await this.getUserById(userId);
            if (!user) {
                return { referrals: [], total: 0, filtered: false };
            }

            const promotionReport = user.promotion_report || [];

            // Get all users referred by this user
            const referredUsers = await this.usersCollection.find({
                referred_by: userId.toString()
            }, {
                projection: {
                    user_id: 1,
                    first_name: 1,
                    last_name: 1,
                    username: 1,
                    created_at: 1,
                    balance: 1
                }
            }).toArray();

            // Combine promotion report data with user details
            let detailedReferrals = promotionReport.map(report => {
                const referredUser = referredUsers.find(u => u.user_id === report.referred_user_id);

                return {
                    ...report,
                    referred_user_name: report.referred_user_name ||
                        (referredUser ? `${referredUser.first_name || ''} ${referredUser.last_name || ''}`.trim() : 'Unknown User'),
                    referred_user_username: referredUser?.username || 'no_username',
                    referred_user_join_date: referredUser?.created_at || null,
                    referred_user_balance: referredUser?.balance || 0
                };
            });

            // Apply date filter if provided
            if (filterDate) {
                const targetDate = new Date(filterDate + 'T00:00:00.000Z');
                const startOfDay = Math.floor(targetDate.getTime() / 1000);
                const endOfDay = startOfDay + (24 * 60 * 60) - 1;

                detailedReferrals = detailedReferrals.filter(referral => {
                    if (!referral.referred_user_join_date) return false;
                    return referral.referred_user_join_date >= startOfDay &&
                           referral.referred_user_join_date <= endOfDay;
                });
            }

            // Sort by earnings (highest first)
            detailedReferrals.sort((a, b) => (b.amount_got || 0) - (a.amount_got || 0));

            return {
                referrals: detailedReferrals,
                total: detailedReferrals.length,
                originalTotal: promotionReport.length,
                filtered: !!filterDate,
                filterDate: filterDate
            };

        } catch (error) {
            console.error('Error getting user referrals with details:', error);
            throw error;
        }
    }

    async getTodayEarnings(userId) {
        try {
            const today = new Date();
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const startOfDayTimestamp = Math.floor(startOfDay.getTime() / 1000);

            // Get referrals that joined today
            const todayReferrals = await this.usersCollection.find({
                referred_by: userId.toString(),
                created_at: { $gte: startOfDayTimestamp }
            }).toArray();

            // Calculate earnings from today's referrals
            const user = await this.getUserById(userId);
            const promotionReport = user?.promotion_report || [];

            let todayEarnings = 0;
            todayReferrals.forEach(referral => {
                const report = promotionReport.find(p => p.referred_user_id === referral.user_id);
                if (report) {
                    todayEarnings += report.amount_got || 0;
                }
            });

            return {
                amount: todayEarnings,
                referrals: todayReferrals.length,
                date: today.toISOString().split('T')[0]
            };
        } catch (error) {
            console.error('Error getting today earnings:', error);
            return { amount: 0, referrals: 0, date: new Date().toISOString().split('T')[0] };
        }
    }

    async getWithdrawalHistory(userId) {
        try {
            // Check if withdrawals collection exists
            const withdrawalsCollection = this.db.collection('withdrawals');

            const withdrawals = await withdrawalsCollection.find({
                user_id: parseInt(userId)
            }).sort({ created_at: -1 }).limit(20).toArray();

            // Also check user's withdrawal_reports field
            const user = await this.getUserById(userId);
            const userWithdrawals = user?.withdrawal_reports || [];

            // Combine and format withdrawal history
            const combinedHistory = [
                ...withdrawals.map(w => ({
                    amount: w.amount,
                    status: w.status,
                    method: w.method,
                    date: w.date || new Date(w.created_at * 1000).toLocaleDateString(),
                    created_at: w.created_at,
                    source: 'database'
                })),
                ...userWithdrawals.map(w => ({
                    amount: w.amount,
                    status: w.status,
                    method: w.method || 'bank',
                    date: w.date,
                    created_at: w.timestamp,
                    source: 'user_record'
                }))
            ];

            // Sort by date and remove duplicates
            return combinedHistory
                .sort((a, b) => (b.created_at || 0) - (a.created_at || 0))
                .slice(0, 10);

        } catch (error) {
            console.error('Error getting withdrawal history:', error);
            return [];
        }
    }

    async getGiftCodeHistory(userId) {
        try {
            const giftCodesCollection = this.db.collection('gift_codes');
            const linkBasedCodesCollection = this.db.collection('link_based_codes');

            // Find gift codes redeemed by this user
            const giftCodes = await giftCodesCollection.find({
                redeemed_by: parseInt(userId)
            }).toArray();

            const linkCodes = await linkBasedCodesCollection.find({
                claimed_users: parseInt(userId)
            }).toArray();

            const history = [
                ...giftCodes.map(code => ({
                    code: code.code,
                    amount: code.amount,
                    type: 'gift_code',
                    redeemed_at: code.last_used || code.created_at
                })),
                ...linkCodes.map(code => ({
                    code: code.code,
                    amount: code.amount,
                    type: 'link_code',
                    redeemed_at: code.created_at
                }))
            ];

            return history.sort((a, b) => (b.redeemed_at || 0) - (a.redeemed_at || 0));

        } catch (error) {
            console.error('Error getting gift code history:', error);
            return [];
        }
    }

    async getCustomReferralLinks(userId) {
        try {
            const customReferralsCollection = this.db.collection('custom_referrals');

            const customLinks = await customReferralsCollection.find({
                user_id: parseInt(userId)
            }).toArray();

            return customLinks.map(link => ({
                parameter: link.custom_param,
                created_at: link.created_at || 0,
                uses: 0 // We'll calculate this separately if needed
            }));

        } catch (error) {
            console.error('Error getting custom referral links:', error);
            return [];
        }
    }

    async getDailyEarningsBreakdown(userId) {
        try {
            const user = await this.getUserById(userId);
            const promotionReport = user?.promotion_report || [];

            // Get all referrals for this user
            const referrals = await this.usersCollection.find({
                referred_by: userId.toString()
            }).toArray();

            // Group earnings by date
            const dailyBreakdown = {};

            referrals.forEach(referral => {
                if (referral.created_at) {
                    const date = new Date(referral.created_at * 1000).toISOString().split('T')[0];

                    if (!dailyBreakdown[date]) {
                        dailyBreakdown[date] = {
                            date,
                            referrals: 0,
                            earnings: 0
                        };
                    }

                    dailyBreakdown[date].referrals++;

                    // Find corresponding earnings
                    const report = promotionReport.find(p => p.referred_user_id === referral.user_id);
                    if (report) {
                        dailyBreakdown[date].earnings += report.amount_got || 0;
                    }
                }
            });

            // Convert to array and sort by date (newest first)
            return Object.values(dailyBreakdown)
                .sort((a, b) => new Date(b.date) - new Date(a.date))
                .slice(0, 30); // Last 30 days

        } catch (error) {
            console.error('Error getting daily earnings breakdown:', error);
            return [];
        }
    }
}

class DateAnalyzer {
    constructor(db) {
        this.db = db;
        this.usersCollection = db.collection('users');
        this.timezone = 'Asia/Kolkata';
    }

    // Convert date string to timestamp range for the specified timezone
    getDateRange(dateStr) {
        const date = new Date(dateStr + 'T00:00:00.000Z');
        const startOfDay = new Date(date.getTime() - (5.5 * 60 * 60 * 1000)); // UTC to IST offset
        const endOfDay = new Date(startOfDay.getTime() + (24 * 60 * 60 * 1000) - 1);

        return {
            start: Math.floor(startOfDay.getTime() / 1000),
            end: Math.floor(endOfDay.getTime() / 1000)
        };
    }

    // Get preset date ranges
    getPresetDateRange(preset) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        switch (preset) {
            case 'today':
                return this.getDateRange(today.toISOString().split('T')[0]);

            case 'yesterday':
                const yesterday = new Date(today.getTime() - (24 * 60 * 60 * 1000));
                return this.getDateRange(yesterday.toISOString().split('T')[0]);

            case 'last7days':
                const week = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
                return {
                    start: Math.floor(week.getTime() / 1000),
                    end: Math.floor(today.getTime() / 1000) + (24 * 60 * 60) - 1
                };

            case 'last30days':
                const month = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
                return {
                    start: Math.floor(month.getTime() / 1000),
                    end: Math.floor(today.getTime() / 1000) + (24 * 60 * 60) - 1
                };

            case 'thismonth':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return {
                    start: Math.floor(monthStart.getTime() / 1000),
                    end: Math.floor(today.getTime() / 1000) + (24 * 60 * 60) - 1
                };

            default:
                return this.getDateRange(today.toISOString().split('T')[0]);
        }
    }

    // Analyze users who joined on a specific date
    async getDateAnalysis(dateStr, options = {}) {
        const { sortBy = 'earnings', sortOrder = 'desc', page = 1, limit = 50 } = options;
        const dateRange = this.getDateRange(dateStr);

        return await this.performAnalysis(dateRange, { sortBy, sortOrder, page, limit }, dateStr);
    }

    // Analyze users who joined in a date range
    async getDateRangeAnalysis(startDateStr, endDateStr, options = {}) {
        const { sortBy = 'earnings', sortOrder = 'desc', page = 1, limit = 50 } = options;
        const startRange = this.getDateRange(startDateStr);
        const endRange = this.getDateRange(endDateStr);

        const dateRange = {
            start: startRange.start,
            end: endRange.end
        };

        return await this.performAnalysis(dateRange, { sortBy, sortOrder, page, limit }, `${startDateStr} to ${endDateStr}`);
    }

    // Analyze users using preset date ranges
    async getPresetAnalysis(preset, options = {}) {
        const { sortBy = 'earnings', sortOrder = 'desc', page = 1, limit = 50 } = options;
        const dateRange = this.getPresetDateRange(preset);

        return await this.performAnalysis(dateRange, { sortBy, sortOrder, page, limit }, preset);
    }

    // Core analysis method - optimized for performance
    async performAnalysis(dateRange, options, dateLabel) {
        try {
            console.log(`Starting analysis for ${dateLabel}, date range: ${dateRange.start} to ${dateRange.end}`);

            // Get users who joined in the date range with basic info only
            const usersInRange = await this.usersCollection.find({
                created_at: {
                    $gte: dateRange.start,
                    $lte: dateRange.end
                }
            }, {
                projection: {
                    user_id: 1,
                    first_name: 1,
                    last_name: 1,
                    username: 1,
                    balance: 1,
                    created_at: 1,
                    referred_by: 1,
                    promotion_report: 1
                }
            }).limit(1000).toArray(); // Limit to prevent timeout

            console.log(`Found ${usersInRange.length} users in date range`);

            if (usersInRange.length === 0) {
                return {
                    dateRange: dateLabel,
                    summary: this.getEmptySummary(dateRange),
                    users: [],
                    pagination: {
                        page: 1,
                        limit: options.limit,
                        total: 0,
                        totalPages: 0
                    },
                    timestamp: new Date().toISOString()
                };
            }

            // Process users in smaller batches for better performance
            const batchSize = 50;
            const userAnalysis = [];

            for (let i = 0; i < usersInRange.length; i += batchSize) {
                const batch = usersInRange.slice(i, i + batchSize);
                const batchResults = await Promise.all(batch.map(user => this.analyzeUserSimple(user, dateRange)));
                userAnalysis.push(...batchResults);

                // Add small delay to prevent overwhelming the database
                if (i + batchSize < usersInRange.length) {
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
            }

            console.log(`Analyzed ${userAnalysis.length} users`);

            // Sort the results
            const sortedUsers = this.sortUsers(userAnalysis, options.sortBy, options.sortOrder);

            // Paginate results
            const startIndex = (options.page - 1) * options.limit;
            const paginatedUsers = sortedUsers.slice(startIndex, startIndex + options.limit);

            // Calculate summary statistics
            const summary = this.calculateSummary(userAnalysis, dateRange);

            return {
                dateRange: dateLabel,
                summary,
                users: paginatedUsers,
                pagination: {
                    page: options.page,
                    limit: options.limit,
                    total: sortedUsers.length,
                    totalPages: Math.ceil(sortedUsers.length / options.limit)
                },
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('Error in performAnalysis:', error);
            throw error;
        }
    }

    // Simplified user analysis for better performance
    async analyzeUserSimple(user, dateRange) {
        const promotionReport = user.promotion_report || [];

        // For performance, we'll skip the detailed referral lookup for now
        // and focus on the user's own data and basic referrer info

        // Get referrer information (cached lookup)
        let referrerInfo = null;
        if (user.referred_by && user.referred_by !== 'None' && !isNaN(user.referred_by)) {
            // Simple lookup without full user details for performance
            referrerInfo = {
                user_id: parseInt(user.referred_by),
                name: 'Referrer',
                username: 'referrer'
            };
        }

        // Calculate total earnings from promotion report
        const totalEarnings = promotionReport.reduce((sum, report) => sum + (report.amount_got || 0), 0);

        return {
            user_id: user.user_id,
            name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unknown User',
            username: user.username || 'no_username',
            balance: user.balance || 0,
            joinedAt: user.created_at,
            joinedDate: new Date(user.created_at * 1000).toISOString().split('T')[0],
            joinedTime: new Date(user.created_at * 1000).toLocaleTimeString('en-US', { timeZone: this.timezone }),
            referralsInRange: 0, // Will be calculated separately if needed
            earningsInRange: 0, // Will be calculated separately if needed
            totalReferrals: promotionReport.length,
            totalEarnings,
            referrer: referrerInfo,
            referralDetails: []
        };
    }

    // Get empty summary for when no data is found
    getEmptySummary(dateRange) {
        return {
            totalUsers: 0,
            totalEarnings: 0,
            totalReferrals: 0,
            averageEarningsPerUser: 0,
            averageReferralsPerUser: 0,
            topReferrer: null,
            mostActiveReferrer: null,
            dateRange: {
                start: dateRange.start,
                end: dateRange.end,
                startDate: new Date(dateRange.start * 1000).toISOString().split('T')[0],
                endDate: new Date(dateRange.end * 1000).toISOString().split('T')[0]
            }
        };
    }

    // Sort users based on criteria
    sortUsers(users, sortBy, sortOrder) {
        const multiplier = sortOrder === 'desc' ? -1 : 1;

        return users.sort((a, b) => {
            let aValue, bValue;

            switch (sortBy) {
                case 'earnings':
                    aValue = a.earningsInRange;
                    bValue = b.earningsInRange;
                    break;
                case 'referrals':
                    aValue = a.referralsInRange;
                    bValue = b.referralsInRange;
                    break;
                case 'joinTime':
                    aValue = a.joinedAt;
                    bValue = b.joinedAt;
                    break;
                case 'totalEarnings':
                    aValue = a.totalEarnings;
                    bValue = b.totalEarnings;
                    break;
                case 'name':
                    aValue = a.name.toLowerCase();
                    bValue = b.name.toLowerCase();
                    break;
                default:
                    aValue = a.earningsInRange;
                    bValue = b.earningsInRange;
            }

            if (aValue < bValue) return multiplier;
            if (aValue > bValue) return -multiplier;
            return 0;
        });
    }

    // Calculate summary statistics
    calculateSummary(users, dateRange) {
        const totalUsers = users.length;
        const totalEarnings = users.reduce((sum, user) => sum + user.earningsInRange, 0);
        const totalReferrals = users.reduce((sum, user) => sum + user.referralsInRange, 0);

        // Find top referrer
        const topReferrer = users.reduce((top, user) => {
            return user.earningsInRange > (top?.earningsInRange || 0) ? user : top;
        }, null);

        // Find most active referrer
        const mostActiveReferrer = users.reduce((top, user) => {
            return user.referralsInRange > (top?.referralsInRange || 0) ? user : top;
        }, null);

        return {
            totalUsers,
            totalEarnings,
            totalReferrals,
            averageEarningsPerUser: totalUsers > 0 ? Math.round((totalEarnings / totalUsers) * 100) / 100 : 0,
            averageReferralsPerUser: totalUsers > 0 ? Math.round((totalReferrals / totalUsers) * 100) / 100 : 0,
            topReferrer: topReferrer ? {
                user_id: topReferrer.user_id,
                name: topReferrer.name,
                earnings: topReferrer.earningsInRange,
                referrals: topReferrer.referralsInRange
            } : null,
            mostActiveReferrer: mostActiveReferrer ? {
                user_id: mostActiveReferrer.user_id,
                name: mostActiveReferrer.name,
                referrals: mostActiveReferrer.referralsInRange,
                earnings: mostActiveReferrer.earningsInRange
            } : null,
            dateRange: {
                start: dateRange.start,
                end: dateRange.end,
                startDate: new Date(dateRange.start * 1000).toISOString().split('T')[0],
                endDate: new Date(dateRange.end * 1000).toISOString().split('T')[0]
            }
        };
    }
}

// API Routes
app.get('/api/health', async (req, res) => {
    try {
        // Test database connection
        if (!db) {
            return res.status(503).json({
                status: 'ERROR',
                message: 'Database not connected',
                timestamp: new Date().toISOString()
            });
        }

        // Ping database
        await db.command({ ping: 1 });

        // Get basic stats
        const userCount = await db.collection('users').estimatedDocumentCount();

        res.json({
            status: 'OK',
            timestamp: new Date().toISOString(),
            database: 'connected',
            userCount: userCount,
            version: '1.0.0'
        });
    } catch (error) {
        console.error('Health check failed:', error);
        res.status(503).json({
            status: 'ERROR',
            message: 'Health check failed',
            timestamp: new Date().toISOString()
        });
    }
});

app.get('/api/database-stats', async (req, res) => {
    try {
        if (!db) {
            return res.status(500).json({ error: 'Database not connected' });
        }

        const stats = await Promise.all([
            db.collection('users').estimatedDocumentCount(),
            db.collection('custom_referrals').estimatedDocumentCount(),
            db.collection('users').countDocuments({ banned: { $ne: true } }),
            db.collection('users').countDocuments({ referred_by: { $ne: 'None' } })
        ]);

        res.json({
            totalUsers: stats[0],
            customReferrals: stats[1],
            activeUsers: stats[2],
            referredUsers: stats[3],
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting database stats:', error);
        res.status(500).json({ error: 'Failed to get database statistics' });
    }
});

// Date-based analysis endpoint
app.get('/api/analysis/date/:date', async (req, res) => {
    try {
        const { date } = req.params;
        const { sortBy = 'earnings', sortOrder = 'desc', page = 1, limit = 50 } = req.query;

        const analysis = await analyzer.getDateAnalysis(date, { sortBy, sortOrder, page: parseInt(page), limit: parseInt(limit) });
        res.json(analysis);
    } catch (error) {
        console.error('Error in date analysis:', error);
        res.status(500).json({ error: error.message });
    }
});

// Date range analysis endpoint
app.get('/api/analysis/range/:startDate/:endDate', async (req, res) => {
    try {
        const { startDate, endDate } = req.params;
        const { sortBy = 'earnings', sortOrder = 'desc', page = 1, limit = 50 } = req.query;

        const analysis = await analyzer.getDateRangeAnalysis(startDate, endDate, { sortBy, sortOrder, page: parseInt(page), limit: parseInt(limit) });
        res.json(analysis);
    } catch (error) {
        console.error('Error in date range analysis:', error);
        res.status(500).json({ error: error.message });
    }
});

// Quick preset analysis endpoint
app.get('/api/analysis/preset/:preset', async (req, res) => {
    try {
        const { preset } = req.params;
        const { sortBy = 'earnings', sortOrder = 'desc', page = 1, limit = 50 } = req.query;

        const analysis = await analyzer.getPresetAnalysis(preset, { sortBy, sortOrder, page: parseInt(page), limit: parseInt(limit) });
        res.json(analysis);
    } catch (error) {
        console.error('Error in preset analysis:', error);
        res.status(500).json({ error: error.message });
    }
});

app.get('/api/user/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        
        if (!db) {
            return res.status(500).json({ error: 'Database not connected' });
        }

        const service = new ReferralChainService(db);
        const user = await service.getUserById(userId);
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Remove sensitive information
        const { _id, ...userInfo } = user;
        
        res.json(userInfo);
    } catch (error) {
        console.error('Error in /api/user:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Get detailed referrals for a user with optional date filtering
app.get('/api/user/:userId/referrals', async (req, res) => {
    try {
        const { userId } = req.params;
        const { date } = req.query;

        if (!db) {
            return res.status(500).json({ error: 'Database not connected' });
        }

        const service = new ReferralChainService(db);
        const referrals = await service.getUserReferralsWithDetails(userId, date);
        res.json(referrals);
    } catch (error) {
        console.error('Error fetching user referrals:', error);
        res.status(500).json({ error: 'Failed to fetch referral data' });
    }
});

app.get('/api/referral-chain/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const {
            maxDepth = 5,
            includeBanned = 'false',
            includeCustomReferrals = 'true'
        } = req.query;

        if (!db) {
            return res.status(500).json({ error: 'Database not connected' });
        }

        const service = new ReferralChainService(db);
        const chain = await service.buildReferralChain(userId, {
            maxDepth: parseInt(maxDepth),
            includeBanned: includeBanned === 'true',
            includeCustomReferrals: includeCustomReferrals === 'true'
        });

        if (!chain) {
            return res.status(404).json({ error: 'User not found or no referral chain' });
        }

        // Clean the chain to remove circular references
        const cleanChain = service.cleanTreeForJSON(chain);
        res.json(cleanChain);
    } catch (error) {
        console.error('Error in /api/referral-chain:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.get('/api/stats/:userId', async (req, res) => {
    try {
        const { userId } = req.params;

        if (!db) {
            return res.status(500).json({ error: 'Database not connected' });
        }

        const service = new ReferralChainService(db);
        const stats = await service.getReferralStatistics(userId);

        if (!stats) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json(stats);
    } catch (error) {
        console.error('Error in /api/stats:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.get('/api/referrals/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const {
            page = 1,
            limit = 50,
            includeBanned = 'false',
            includeCustomReferrals = 'true'
        } = req.query;

        if (!db) {
            return res.status(500).json({ error: 'Database not connected' });
        }

        const service = new ReferralChainService(db);
        const referralsData = await service.getDirectReferrals(userId, {
            page: parseInt(page),
            limit: parseInt(limit),
            includeBanned: includeBanned === 'true',
            includeCustomReferrals: includeCustomReferrals === 'true'
        });

        res.json(referralsData);
    } catch (error) {
        console.error('Error in /api/referrals:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.get('/api/search/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const {
            q: searchTerm,
            maxDepth = 5,
            includeBanned = 'false'
        } = req.query;

        if (!searchTerm) {
            return res.status(400).json({ error: 'Search term is required' });
        }

        if (!db) {
            return res.status(500).json({ error: 'Database not connected' });
        }

        const service = new ReferralChainService(db);
        const results = await service.searchInChain(userId, searchTerm, {
            maxDepth: parseInt(maxDepth),
            includeBanned: includeBanned === 'true'
        });

        res.json(results);
    } catch (error) {
        console.error('Error in /api/search:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.post('/api/export/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const {
            format = 'json',
            maxDepth = 10,
            includeBanned = false,
            includeCustomReferrals = true
        } = req.body;

        if (!db) {
            return res.status(500).json({ error: 'Database not connected' });
        }

        const service = new ReferralChainService(db);
        const chain = await service.buildReferralChain(userId, {
            maxDepth: parseInt(maxDepth),
            includeBanned,
            includeCustomReferrals
        });

        if (!chain) {
            return res.status(404).json({ error: 'User not found or no referral chain' });
        }

        // Clean the chain to remove circular references
        const cleanChain = service.cleanTreeForJSON(chain);

        if (format === 'csv') {
            // Convert to CSV format
            const csvData = convertChainToCSV(cleanChain);
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', `attachment; filename="referral-chain-${userId}.csv"`);
            res.send(csvData);
        } else {
            // Return JSON format
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Content-Disposition', `attachment; filename="referral-chain-${userId}.json"`);
            res.json(cleanChain);
        }
    } catch (error) {
        console.error('Error in /api/export:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Helper function to convert chain to CSV
function convertChainToCSV(chain) {
    const rows = [];
    rows.push(['User ID', 'Name', 'Username', 'Balance', 'Banned', 'Depth', 'Joined At', 'Referred By']);

    function addNodeToCSV(node, depth = 0) {
        const user = node.user;
        rows.push([
            user.user_id,
            user.first_name || '',
            user.username || '',
            user.balance || 0,
            user.banned ? 'Yes' : 'No',
            depth,
            user.created_at || '',
            user.referred_by || 'None'
        ]);

        if (node.children) {
            node.children.forEach(child => addNodeToCSV(child, depth + 1));
        }
    }

    addNodeToCSV(chain);

    return rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
}

// Serve static files
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({ error: 'Internal server error' });
});

// Global instances
let analyzer;

// Start server
async function startServer() {
    try {
        await connectToDatabase();

        // Initialize the date analyzer
        analyzer = new DateAnalyzer(db);
        console.log('✅ Date analyzer initialized');

        app.listen(PORT, () => {
            console.log(`Referral Chain Visualizer running on port ${PORT}`);
            console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    if (client) {
        await client.close();
    }
    process.exit(0);
});

// For Vercel, export the app
if (process.env.VERCEL) {
    module.exports = app;
} else {
    startServer();
}
