// Final comprehensive verification script
async function runFinalVerification() {
    console.log('🎯 FINAL COMPREHENSIVE DATA ACCURACY VERIFICATION');
    console.log('=' .repeat(80));

    const testCases = [
        {
            userId: '2027123358',
            name: '<PERSON><PERSON><PERSON><PERSON> (Rich Data User)',
            expectedBalance: 1483.91,
            expectedWithdrawals: 200,
            expectedEarnings: 1479,
            expectedReferrals: 1168
        },
        {
            userId: '8153676253', 
            name: '<PERSON><PERSON> (Moderate Data)',
            expectedBalance: 1062,
            expectedWithdrawals: 100,
            expectedEarnings: 7,
            expectedReferrals: 1
        },
        {
            userId: '461991991',
            name: '<PERSON><PERSON><PERSON> (Simple Data)',
            expectedBalance: 62,
            expectedWithdrawals: 0,
            expectedEarnings: 3,
            expectedReferrals: 2
        }
    ];

    let allTestsPassed = true;

    for (const testCase of testCases) {
        console.log(`\n🔍 TESTING: ${testCase.name} (ID: ${testCase.userId})`);
        console.log('-' .repeat(60));

        try {
            // Fetch data from API
            const [userResponse, statsResponse] = await Promise.all([
                fetch(`http://localhost:3001/api/user/${testCase.userId}`),
                fetch(`http://localhost:3001/api/stats/${testCase.userId}`)
            ]);

            if (!userResponse.ok || !statsResponse.ok) {
                console.log('❌ Failed to fetch API data');
                allTestsPassed = false;
                continue;
            }

            const userData = await userResponse.json();
            const statsData = await statsResponse.json();

            // Test 1: Basic Financial Data Accuracy
            console.log('\n💰 Financial Data Accuracy:');
            const balanceMatch = userData.balance === testCase.expectedBalance;
            const withdrawalMatch = userData.successful_withdraw === testCase.expectedWithdrawals;
            const earningsMatch = statsData.totalEarnings === testCase.expectedEarnings;
            const referralsMatch = statsData.totalReferrals === testCase.expectedReferrals;

            console.log(`${balanceMatch ? '✅' : '❌'} Balance: Expected=${testCase.expectedBalance}, Got=${userData.balance}`);
            console.log(`${withdrawalMatch ? '✅' : '❌'} Withdrawals: Expected=${testCase.expectedWithdrawals}, Got=${userData.successful_withdraw}`);
            console.log(`${earningsMatch ? '✅' : '❌'} Earnings: Expected=${testCase.expectedEarnings}, Got=${statsData.totalEarnings}`);
            console.log(`${referralsMatch ? '✅' : '❌'} Referrals: Expected=${testCase.expectedReferrals}, Got=${statsData.totalReferrals}`);

            // Test 2: Calculated Fields Accuracy
            console.log('\n🧮 Calculated Fields Accuracy:');
            const promotionReport = userData.promotion_report || [];
            const calculatedEarnings = promotionReport.reduce((sum, report) => sum + (report.amount_got || 0), 0);
            const earningsCalculationMatch = calculatedEarnings === statsData.totalEarnings;
            
            console.log(`${earningsCalculationMatch ? '✅' : '❌'} Earnings Calculation: Manual=${calculatedEarnings}, API=${statsData.totalEarnings}`);

            const netEarnings = (userData.balance || 0) + (userData.successful_withdraw || 0);
            console.log(`✅ Net Earnings: ${netEarnings} (Balance: ${userData.balance} + Withdrawals: ${userData.successful_withdraw})`);

            // Test 3: Enhanced Features Data Structure
            console.log('\n🔧 Enhanced Features Structure:');
            const hasToday = statsData.todayEarnings && typeof statsData.todayEarnings.amount === 'number';
            const hasWithdrawals = Array.isArray(statsData.withdrawalHistory);
            const hasGiftCodes = Array.isArray(statsData.giftCodeHistory);
            const hasCustomLinks = Array.isArray(statsData.customReferralLinks);
            const hasDailyEarnings = Array.isArray(statsData.dailyEarnings);

            console.log(`${hasToday ? '✅' : '❌'} Today's Earnings Structure: ${hasToday ? 'Valid' : 'Invalid'}`);
            console.log(`${hasWithdrawals ? '✅' : '❌'} Withdrawal History Structure: ${hasWithdrawals ? 'Valid' : 'Invalid'}`);
            console.log(`${hasGiftCodes ? '✅' : '❌'} Gift Code History Structure: ${hasGiftCodes ? 'Valid' : 'Invalid'}`);
            console.log(`${hasCustomLinks ? '✅' : '❌'} Custom Links Structure: ${hasCustomLinks ? 'Valid' : 'Invalid'}`);
            console.log(`${hasDailyEarnings ? '✅' : '❌'} Daily Earnings Structure: ${hasDailyEarnings ? 'Valid' : 'Invalid'}`);

            // Test 4: Date and Time Accuracy
            console.log('\n📅 Date and Time Accuracy:');
            const today = new Date().toISOString().split('T')[0];
            const todayDateMatch = statsData.todayEarnings.date === today;
            console.log(`${todayDateMatch ? '✅' : '❌'} Today's Date: Expected=${today}, Got=${statsData.todayEarnings.date}`);

            // Test 5: Data Consistency Checks
            console.log('\n🔄 Data Consistency Checks:');
            const balanceConsistency = userData.balance === statsData.userBalance;
            const promotionConsistency = promotionReport.length === statsData.promotionReports;
            
            console.log(`${balanceConsistency ? '✅' : '❌'} Balance Consistency: User=${userData.balance}, Stats=${statsData.userBalance}`);
            console.log(`${promotionConsistency ? '✅' : '❌'} Promotion Count Consistency: User=${promotionReport.length}, Stats=${statsData.promotionReports}`);

            // Test 6: Edge Cases and Error Handling
            console.log('\n⚠️ Edge Cases and Error Handling:');
            const handlesNullValues = statsData.todayEarnings.amount >= 0; // Should be 0 or positive
            const handlesEmptyArrays = statsData.giftCodeHistory.length >= 0; // Should handle empty arrays
            
            console.log(`${handlesNullValues ? '✅' : '❌'} Handles Today's Zero Earnings: ${statsData.todayEarnings.amount}`);
            console.log(`${handlesEmptyArrays ? '✅' : '❌'} Handles Empty Gift Code Array: Length=${statsData.giftCodeHistory.length}`);

            // Summary for this user
            const userTestsPassed = balanceMatch && withdrawalMatch && earningsMatch && referralsMatch && 
                                  earningsCalculationMatch && hasToday && hasWithdrawals && hasGiftCodes && 
                                  hasCustomLinks && hasDailyEarnings && todayDateMatch && balanceConsistency && 
                                  promotionConsistency && handlesNullValues && handlesEmptyArrays;

            console.log(`\n📊 User Test Summary: ${userTestsPassed ? '✅ ALL PASSED' : '❌ SOME FAILED'}`);
            
            if (!userTestsPassed) {
                allTestsPassed = false;
            }

        } catch (error) {
            console.error(`❌ Error testing user ${testCase.userId}:`, error.message);
            allTestsPassed = false;
        }
    }

    // Final Summary
    console.log('\n' + '=' .repeat(80));
    console.log(`🎯 FINAL VERIFICATION RESULT: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    console.log('=' .repeat(80));

    if (allTestsPassed) {
        console.log('🎉 The Enhanced Referral Chain Visualizer data is 100% accurate!');
        console.log('✅ All financial calculations are correct');
        console.log('✅ All enhanced features are working properly');
        console.log('✅ All data structures are valid');
        console.log('✅ All edge cases are handled correctly');
    } else {
        console.log('⚠️ Some verification tests failed. Please review the output above.');
    }
}

// Run the final verification
runFinalVerification().catch(console.error);
