/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 10px;
}

.header-content h1 i {
    color: #667eea;
    margin-right: 15px;
}

.header-content p {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 400;
}

/* Search Section */
.search-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-container {
    max-width: 800px;
    margin: 0 auto;
}

.search-box {
    display: flex;
    align-items: center;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    padding: 15px 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box i {
    color: #a0aec0;
    margin-right: 15px;
    font-size: 1.2rem;
}

.search-box input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 1.1rem;
    color: #2d3748;
    outline: none;
}

.search-box input::placeholder {
    color: #a0aec0;
}

.search-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 15px;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.search-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.search-options {
    display: flex;
    gap: 30px;
    justify-content: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.95rem;
    color: #4a5568;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #cbd5e0;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* User Profile Section */
.user-profile-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-container {
    max-width: 1200px;
    margin: 0 auto;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

.profile-avatar {
    color: #667eea;
}

.profile-info {
    flex: 1;
}

.profile-info h2 {
    font-size: 1.8rem;
    color: #2d3748;
    margin-bottom: 5px;
    font-weight: 600;
}

.profile-info p {
    color: #718096;
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    background: #48bb78;
    color: white;
}

.status-badge.banned {
    background: #e53e3e;
}

.profile-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 10px 15px;
    border-radius: 10px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.profile-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 30px;
    background: #f7fafc;
    padding: 5px;
    border-radius: 15px;
}

.tab-btn {
    flex: 1;
    background: transparent;
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    color: #718096;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Overview Tab */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.overview-card {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.overview-card:nth-child(2) .card-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.overview-card:nth-child(3) .card-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.overview-card:nth-child(4) .card-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.card-content h4 {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 5px;
    font-weight: 500;
}

.card-content p {
    font-size: 1.1rem;
    color: #2d3748;
    font-weight: 600;
    margin: 0;
}

/* Financial Tab */
.financial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.financial-card {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.financial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.financial-card h4 {
    font-size: 1rem;
    color: #718096;
    margin-bottom: 15px;
    font-weight: 500;
}

.financial-card .amount {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
}

.balance-card .amount {
    color: #667eea;
}

.earnings-card .amount {
    color: #48bb78;
}

.withdrawals-card .amount {
    color: #ed8936;
}

.net-card .amount {
    color: #9f7aea;
}

/* Enhanced Financial Tab */
.financial-overview {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.today-activity {
    background: #f7fafc;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #e2e8f0;
}

.today-activity h4 {
    font-size: 1.1rem;
    color: #2d3748;
    margin-bottom: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.today-activity h4 i {
    color: #f6ad55;
}

.today-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.today-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.today-label {
    font-weight: 500;
    color: #718096;
    font-size: 0.9rem;
}

.today-value {
    font-weight: 600;
    color: #2d3748;
    font-size: 1.1rem;
}

.earnings-chart {
    background: #f7fafc;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #e2e8f0;
}

.earnings-chart h4 {
    font-size: 1.1rem;
    color: #2d3748;
    margin-bottom: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.earnings-chart h4 i {
    color: #667eea;
}

.chart-container {
    height: 200px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #718096;
    font-style: italic;
}

/* Transactions Tab */
.transactions-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.transaction-section {
    background: #f7fafc;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #e2e8f0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h4 {
    font-size: 1.1rem;
    color: #2d3748;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-header h4 i {
    color: #667eea;
}

.transaction-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.transaction-item {
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    transform: translateX(5px);
}

.transaction-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.transaction-title {
    font-size: 1rem;
    color: #2d3748;
    font-weight: 600;
}

.transaction-details {
    font-size: 0.85rem;
    color: #718096;
}

.transaction-amount {
    text-align: right;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.transaction-amount .amount {
    font-size: 1.1rem;
    font-weight: 600;
    color: #48bb78;
}

.transaction-amount .status {
    font-size: 0.8rem;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.status.pending {
    background: #fed7d7;
    color: #c53030;
}

.status.approved {
    background: #c6f6d5;
    color: #2f855a;
}

.status.rejected {
    background: #fed7d7;
    color: #c53030;
}

.status.completed {
    background: #c6f6d5;
    color: #2f855a;
}

.transaction-date {
    font-size: 0.8rem;
    color: #a0aec0;
}

.empty-transactions {
    text-align: center;
    padding: 40px 20px;
    color: #718096;
    font-style: italic;
}

.empty-transactions i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #cbd5e0;
}

/* Account Tab */
.account-details {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.detail-section {
    background: #f7fafc;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #e2e8f0;
}

.detail-section h4 {
    font-size: 1.1rem;
    color: #2d3748;
    margin-bottom: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.detail-section h4 i {
    color: #667eea;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item .label {
    font-weight: 500;
    color: #718096;
    font-size: 0.9rem;
}

.detail-item .value {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.95rem;
}

.crypto-address {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem !important;
    background: #edf2f7;
    padding: 4px 8px;
    border-radius: 6px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Referrals Tab */
.referrals-content {
    background: #f7fafc;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #e2e8f0;
}

.referrals-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.referrals-header h4 {
    font-size: 1.1rem;
    color: #2d3748;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.referrals-header h4 i {
    color: #f6ad55;
}

.count-badge {
    background: #667eea;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.referrals-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.referral-item {
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.referral-item:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    transform: translateX(5px);
}

.referral-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.referral-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.referral-details h5 {
    font-size: 1rem;
    color: #2d3748;
    margin-bottom: 3px;
    font-weight: 600;
}

.referral-details p {
    font-size: 0.85rem;
    color: #718096;
    margin: 0;
}

.referral-earnings {
    text-align: right;
}

.referral-earnings .amount {
    font-size: 1.1rem;
    font-weight: 600;
    color: #48bb78;
}

.referral-earnings .date {
    font-size: 0.8rem;
    color: #718096;
    margin-top: 3px;
}

/* Referral Date Filter */
.referral-date-filter {
    background: #f7fafc;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e2e8f0;
}

.filter-controls {
    margin-bottom: 15px;
}

.filter-input-group {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-input-group label {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.9rem;
    white-space: nowrap;
}

.date-filter-input {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    background: white;
    transition: all 0.3s ease;
    min-width: 150px;
}

.date-filter-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-apply-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap;
}

.filter-apply-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.filter-clear-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap;
}

.filter-clear-btn:hover {
    background: #c53030;
    transform: translateY(-1px);
}

.filter-status {
    background: #e6fffa;
    border: 1px solid #81e6d9;
    border-radius: 8px;
    padding: 10px 15px;
    color: #234e52;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-status i {
    color: #38b2ac;
}

.filter-status.active {
    background: #fef5e7;
    border-color: #f6ad55;
    color: #744210;
}

.filter-status.active i {
    color: #ed8936;
}

/* Tree Filter Indicator */
.tree-filter-indicator {
    background: #fef5e7;
    border: 1px solid #f6ad55;
    border-radius: 8px;
    padding: 12px 15px;
    margin-bottom: 15px;
    color: #744210;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.tree-filter-indicator i {
    color: #ed8936;
}

.filter-clear-btn-small {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.filter-clear-btn-small:hover {
    background: #c53030;
}

/* Small action buttons for referral items */
.action-btn-small {
    background: #667eea;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 5px;
}

.action-btn-small:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Statistics Section */
.stats-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.5rem;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.stat-content p {
    color: #718096;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Controls Section */
.controls-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.controls-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 15px;
}

.control-btn {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 10px 20px;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.export-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border-color: transparent;
}

.export-btn:hover {
    background: linear-gradient(135deg, #38a169, #2f855a);
}

.search-in-tree {
    position: relative;
    display: flex;
    align-items: center;
}

.search-in-tree input {
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.9rem;
    width: 250px;
    outline: none;
    transition: all 0.3s ease;
}

.search-in-tree input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-btn {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.clear-btn:hover {
    background: #f7fafc;
    color: #4a5568;
}

/* Main Content */
.main-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 30px;
}

/* States */
.loading-state, .empty-state, .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
}

.loading-spinner {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.empty-icon, .error-icon {
    color: #a0aec0;
    margin-bottom: 20px;
}

.error-icon {
    color: #f56565;
}

.empty-state h3, .error-state h3 {
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 10px;
}

.empty-state p, .error-state p {
    color: #718096;
    font-size: 1rem;
    max-width: 500px;
}

.retry-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Database Stats Section */
.database-stats-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-container h3 {
    font-size: 1.3rem;
    color: #2d3748;
    margin-bottom: 20px;
    font-weight: 600;
}

.db-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.db-stat-item {
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.db-stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

.db-stat-value {
    font-size: 1.1rem;
    color: #2d3748;
    font-weight: 600;
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.footer p {
    margin-bottom: 10px;
}

.footer-link {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    font-size: 0.9rem;
    padding: 5px 10px;
    border-radius: 5px;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.footer-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Tree Visualization */
.tree-container {
    min-height: 500px;
}

.tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

.tree-header h3 {
    font-size: 1.5rem;
    color: #2d3748;
    font-weight: 600;
}

.tree-info {
    color: #718096;
    font-size: 0.9rem;
}

.tree-visualization {
    overflow-x: auto;
    overflow-y: visible;
    padding: 20px 0;
}

.tree-node {
    position: relative;
    margin: 20px 0;
    padding-left: 40px;
}

.tree-node.root {
    padding-left: 0;
}

.tree-node.highlighted {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    padding: 10px;
    margin: 10px -10px;
}

.node-content {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    max-width: 600px;
}

.node-content:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.node-content.banned {
    background: #fed7d7;
    border-color: #feb2b2;
}

.node-content.custom-referral {
    background: #e6fffa;
    border-color: #81e6d9;
}

.node-info {
    flex: 1;
}

.node-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 1rem;
    margin-bottom: 5px;
}

.node-details {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: #718096;
}

.node-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.expand-btn {
    background: #667eea;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.expand-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

.expand-btn.expanded {
    background: #e53e3e;
}

.expand-btn.expanded:hover {
    background: #c53030;
}

.info-btn {
    background: #48bb78;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.info-btn:hover {
    background: #38a169;
    transform: scale(1.1);
}

.node-children {
    margin-left: 20px;
    position: relative;
}

.node-children::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 0;
    bottom: 20px;
    width: 2px;
    background: #e2e8f0;
}

.tree-node:not(.root)::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 20px;
    height: 2px;
    background: #e2e8f0;
}

.tree-node:not(.root)::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 50%;
    width: 2px;
    background: #e2e8f0;
}

.tree-node:last-child::after {
    display: none;
}

.loading-more {
    text-align: center;
    padding: 20px;
    color: #718096;
    font-style: italic;
}

.load-more-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin: 10px auto;
    display: block;
}

.load-more-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    font-size: 1.3rem;
    color: #2d3748;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #a0aec0;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f7fafc;
    color: #4a5568;
}

.modal-body {
    padding: 30px;
}

.user-detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.user-detail-item {
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.user-detail-label {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
    margin-bottom: 5px;
}

.user-detail-value {
    font-size: 1rem;
    color: #2d3748;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .search-box {
        flex-direction: column;
        gap: 15px;
    }

    .search-btn {
        margin-left: 0;
        width: 100%;
    }

    .search-options {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .controls-container {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        justify-content: center;
    }

    .search-in-tree input {
        width: 100%;
    }

    .tree-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .node-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .node-details {
        flex-direction: column;
        gap: 5px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-header, .modal-body {
        padding: 20px;
    }

    .user-detail-grid {
        grid-template-columns: 1fr;
    }

    /* Profile section responsive */
    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .profile-actions {
        justify-content: center;
    }

    .profile-tabs {
        flex-direction: column;
        gap: 5px;
    }

    .tab-btn {
        justify-content: flex-start;
        padding: 15px 20px;
    }

    .overview-grid {
        grid-template-columns: 1fr;
    }

    .financial-grid {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .crypto-address {
        max-width: 100%;
        word-break: break-all;
    }

    /* Enhanced financial tab responsive */
    .today-stats {
        grid-template-columns: 1fr;
    }

    .today-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .chart-container {
        height: 150px;
    }

    /* Transactions responsive */
    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .transaction-amount {
        align-self: flex-end;
        text-align: right;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    /* Referral filter responsive */
    .filter-input-group {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .date-filter-input {
        min-width: auto;
        width: 100%;
    }

    .filter-apply-btn,
    .filter-clear-btn {
        justify-content: center;
        width: 100%;
    }
}

/* Date Analysis Section */
.date-analysis-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.analysis-container {
    max-width: 1400px;
    margin: 0 auto;
}

.analysis-header {
    text-align: center;
    margin-bottom: 30px;
}

.analysis-header h2 {
    font-size: 2rem;
    color: #2d3748;
    margin-bottom: 10px;
    font-weight: 600;
}

.analysis-header p {
    color: #718096;
    font-size: 1.1rem;
}

/* Date Filter Controls */
.date-filter-controls {
    background: #f7fafc;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #e2e8f0;
}

.filter-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 25px;
    background: #edf2f7;
    padding: 5px;
    border-radius: 12px;
}

.filter-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    color: #718096;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.filter-tab:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.filter-tab.active {
    background: #667eea;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.filter-content {
    display: none;
}

.filter-content.active {
    display: block;
}

.date-input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.date-input-group label {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.9rem;
}

.date-input {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.date-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.date-range-group {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 20px;
    align-items: end;
}

.analyze-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    height: fit-content;
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.preset-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.preset-btn {
    background: white;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 15px 20px;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.preset-btn:hover {
    background: #f7fafc;
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
}

/* Analysis Results */
.analysis-results {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.analysis-summary {
    background: #f7fafc;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #e2e8f0;
}

.analysis-summary h3 {
    font-size: 1.3rem;
    color: #2d3748;
    margin-bottom: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.summary-card:nth-child(2) .summary-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.summary-card:nth-child(3) .summary-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.summary-card:nth-child(4) .summary-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.summary-content h4 {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 5px;
    font-weight: 500;
}

.summary-content p {
    font-size: 1.3rem;
    color: #2d3748;
    font-weight: 700;
    margin: 0;
}

/* Results Controls */
.results-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sort-controls label {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.9rem;
}

.sort-select {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    background: white;
    cursor: pointer;
}

.sort-select:focus {
    outline: none;
    border-color: #667eea;
}

.sort-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.sort-btn:hover {
    background: #5a67d8;
}

.export-controls {
    display: flex;
    gap: 10px;
}

.export-btn {
    background: white;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.export-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

/* Results Table */
.results-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
    margin-bottom: 25px;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
}

.results-table th {
    background: #f7fafc;
    color: #2d3748;
    font-weight: 600;
    padding: 15px 12px;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
    font-size: 0.9rem;
}

.results-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f5f9;
    font-size: 0.9rem;
    color: #4a5568;
}

.results-table tr:hover {
    background: #f7fafc;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.user-name {
    font-weight: 600;
    color: #2d3748;
}

.user-details {
    font-size: 0.8rem;
    color: #718096;
}

.earnings-cell {
    font-weight: 600;
    color: #48bb78;
}

.referrals-cell {
    font-weight: 600;
    color: #667eea;
}

.referrer-info {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.referrer-name {
    font-weight: 500;
    color: #2d3748;
}

.referrer-id {
    font-size: 0.8rem;
    color: #718096;
}

.action-btn-small {
    background: #667eea;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn-small:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Pagination */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.pagination-btn {
    background: white;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.pagination-btn:hover:not(:disabled) {
    background: #f7fafc;
    border-color: #667eea;
    color: #667eea;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.9rem;
}

/* Loading and Empty States */
.analysis-loading {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.analysis-empty {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.analysis-empty i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #cbd5e0;
}

.analysis-empty h3 {
    font-size: 1.3rem;
    color: #4a5568;
    margin-bottom: 10px;
}

.analysis-empty p {
    font-size: 1rem;
    color: #718096;
}

/* Responsive Design for Date Analysis */
@media (max-width: 768px) {
    .date-range-group {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .preset-buttons {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .results-controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .sort-controls {
        flex-wrap: wrap;
        gap: 10px;
    }

    .export-controls {
        justify-content: center;
    }

    .results-table-container {
        overflow-x: auto;
    }

    .results-table {
        min-width: 800px;
    }

    .pagination-controls {
        flex-direction: column;
        gap: 15px;
    }
}
