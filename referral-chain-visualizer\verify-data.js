/**
 * Data Verification Script for Referral Chain Visualizer
 * Compares API responses with raw database data to ensure accuracy
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

async function verifyDataAccuracy() {
    console.log('🔍 Verifying data accuracy...\n');

    const mongoUri = process.env.MONGODB_URI;
    const dbName = process.env.DATABASE_NAME || 'referral_bot';

    let client;
    try {
        client = new MongoClient(mongoUri);
        await client.connect();
        const db = client.db(dbName);
        const usersCollection = db.collection('users');

        // Test users
        const testUsers = ['8153676253', '2027123358', '461991991'];

        for (const userId of testUsers) {
            console.log(`\n📊 Testing User ID: ${userId}`);
            console.log('=' .repeat(50));

            // Get raw database data
            const user = await usersCollection.findOne({ user_id: parseInt(userId) });
            if (!user) {
                console.log('❌ User not found in database');
                continue;
            }

            // Calculate expected values from raw data
            const promotionReport = user.promotion_report || [];
            const expectedEarnings = promotionReport.reduce((sum, report) => sum + (report.amount_got || 0), 0);
            
            const directReferrals = await usersCollection.countDocuments({
                referred_by: userId.toString()
            });
            
            const activeReferrals = await usersCollection.countDocuments({
                referred_by: userId.toString(),
                banned: { $ne: true }
            });

            // Get API responses
            const apiStats = await fetch(`http://localhost:3001/api/stats/${userId}`)
                .then(res => res.json())
                .catch(err => ({ error: err.message }));

            const apiUser = await fetch(`http://localhost:3001/api/user/${userId}`)
                .then(res => res.json())
                .catch(err => ({ error: err.message }));

            // Compare values
            console.log('\n🔍 Raw Database Values:');
            console.log(`  User Balance: ₹${user.balance || 0}`);
            console.log(`  Direct Referrals: ${directReferrals}`);
            console.log(`  Active Referrals: ${activeReferrals}`);
            console.log(`  Promotion Reports: ${promotionReport.length}`);
            console.log(`  Expected Earnings: ₹${expectedEarnings}`);

            console.log('\n📡 API Response Values:');
            if (apiStats.error) {
                console.log(`  ❌ Stats API Error: ${apiStats.error}`);
            } else {
                console.log(`  User Balance: ₹${apiStats.userBalance}`);
                console.log(`  Total Referrals: ${apiStats.totalReferrals}`);
                console.log(`  Active Referrals: ${apiStats.activeReferrals}`);
                console.log(`  Promotion Reports: ${apiStats.promotionReports}`);
                console.log(`  Total Earnings: ₹${apiStats.totalEarnings}`);
            }

            console.log('\n✅ Accuracy Check:');
            if (!apiStats.error) {
                const balanceMatch = Math.abs((apiStats.userBalance || 0) - (user.balance || 0)) < 0.01;
                const referralsMatch = apiStats.totalReferrals === directReferrals;
                const activeMatch = apiStats.activeReferrals === activeReferrals;
                const earningsMatch = Math.abs((apiStats.totalEarnings || 0) - expectedEarnings) < 0.01;
                const reportsMatch = apiStats.promotionReports === promotionReport.length;

                console.log(`  Balance: ${balanceMatch ? '✅' : '❌'} ${balanceMatch ? 'Accurate' : 'MISMATCH'}`);
                console.log(`  Referrals: ${referralsMatch ? '✅' : '❌'} ${referralsMatch ? 'Accurate' : 'MISMATCH'}`);
                console.log(`  Active: ${activeMatch ? '✅' : '❌'} ${activeMatch ? 'Accurate' : 'MISMATCH'}`);
                console.log(`  Earnings: ${earningsMatch ? '✅' : '❌'} ${earningsMatch ? 'Accurate' : 'MISMATCH'}`);
                console.log(`  Reports: ${reportsMatch ? '✅' : '❌'} ${reportsMatch ? 'Accurate' : 'MISMATCH'}`);

                if (balanceMatch && referralsMatch && activeMatch && earningsMatch && reportsMatch) {
                    console.log('\n🎉 ALL DATA ACCURATE! ✅');
                } else {
                    console.log('\n⚠️  DATA DISCREPANCIES FOUND! ❌');
                }
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('🎯 Data verification completed!');

    } catch (error) {
        console.error('❌ Verification failed:', error);
    } finally {
        if (client) {
            await client.close();
        }
    }
}

// Helper function to make HTTP requests
async function fetch(url) {
    const https = require('https');
    const http = require('http');
    const urlModule = require('url');
    
    return new Promise((resolve, reject) => {
        const parsedUrl = urlModule.parse(url);
        const client = parsedUrl.protocol === 'https:' ? https : http;
        
        const req = client.get(url, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    resolve(JSON.parse(data));
                } catch (e) {
                    reject(new Error('Invalid JSON response'));
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(10000, () => reject(new Error('Request timeout')));
    });
}

// Run verification
verifyDataAccuracy().catch(console.error);
