class ReferralChainVisualizer {
    constructor() {
        this.currentUserId = null;
        this.currentChain = null;
        this.expandedNodes = new Set();
        this.searchResults = [];
        this.isLoading = false;
        this.currentUserData = null;
        this.currentAnalysisData = null;
        this.currentPage = 1;
        this.lastUsedPreset = null;
        this.currentReferralFilter = null;
        this.originalReferralData = null;

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Search functionality
        const searchBtn = document.getElementById('searchBtn');
        const userIdInput = document.getElementById('userIdInput');
        const retryBtn = document.getElementById('retryBtn');

        searchBtn.addEventListener('click', () => this.searchReferralChain());
        userIdInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchReferralChain();
            }
        });
        retryBtn.addEventListener('click', () => this.searchReferralChain());

        // Control buttons
        document.getElementById('expandAllBtn').addEventListener('click', () => this.expandAll());
        document.getElementById('collapseAllBtn').addEventListener('click', () => this.collapseAll());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportData());

        // Tree search
        const treeSearchInput = document.getElementById('treeSearchInput');
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        
        treeSearchInput.addEventListener('input', (e) => this.searchInTree(e.target.value));
        clearSearchBtn.addEventListener('click', () => this.clearTreeSearch());

        // Modal
        const modal = document.getElementById('userModal');
        const modalClose = document.getElementById('modalClose');

        modalClose.addEventListener('click', () => this.closeModal());
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });

        // Footer buttons
        document.getElementById('showDateAnalysisBtn').addEventListener('click', () => this.toggleDateAnalysis());
        document.getElementById('showStatsBtn').addEventListener('click', () => this.toggleDatabaseStats());
        document.getElementById('refreshStatsBtn').addEventListener('click', () => this.loadDatabaseStats());
        document.getElementById('healthCheckBtn').addEventListener('click', () => this.performHealthCheck());

        // Profile section buttons
        document.getElementById('refreshUserBtn').addEventListener('click', () => this.refreshUserProfile());
        document.getElementById('exportUserBtn').addEventListener('click', () => this.exportUserData());

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // Date analysis event listeners
        this.initializeDateAnalysisListeners();

        // Referral filter event listeners
        this.initializeReferralFilterListeners();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    async searchReferralChain() {
        const userIdInput = document.getElementById('userIdInput');
        const userId = userIdInput.value.trim();

        if (!userId) {
            this.showError('Please enter a valid User ID');
            return;
        }

        if (!/^\d+$/.test(userId)) {
            this.showError('User ID must be a number');
            return;
        }

        this.currentUserId = userId;
        this.showLoading();

        try {
            // Get user info first
            const userResponse = await fetch(`/api/user/${userId}`);
            if (!userResponse.ok) {
                if (userResponse.status === 404) {
                    throw new Error('User not found');
                }
                throw new Error('Failed to fetch user data');
            }

            const userData = await userResponse.json();

            // Get referral chain
            const includeCustomReferrals = document.getElementById('includeCustomReferrals').checked;
            const showBannedUsers = document.getElementById('showBannedUsers').checked;

            const chainResponse = await fetch(`/api/referral-chain/${userId}?` + new URLSearchParams({
                maxDepth: '5',
                includeBanned: showBannedUsers.toString(),
                includeCustomReferrals: includeCustomReferrals.toString()
            }));

            if (!chainResponse.ok) {
                throw new Error('Failed to fetch referral chain');
            }

            const chain = await chainResponse.json();
            this.currentChain = chain;

            // Get statistics
            const statsResponse = await fetch(`/api/stats/${userId}`);
            let stats = null;
            if (statsResponse.ok) {
                stats = await statsResponse.json();
            }

            this.displayResults(chain, stats, userData);

        } catch (error) {
            console.error('Error fetching referral chain:', error);
            this.showError(error.message);
        }
    }

    showLoading() {
        this.isLoading = true;
        this.updateButtonState();
        
        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('errorState').style.display = 'none';
        document.getElementById('treeContainer').style.display = 'none';
        document.getElementById('statsSection').style.display = 'none';
        document.getElementById('controlsSection').style.display = 'none';
        document.getElementById('loadingState').style.display = 'flex';
    }

    showError(message) {
        this.isLoading = false;
        this.updateButtonState();
        
        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('treeContainer').style.display = 'none';
        document.getElementById('statsSection').style.display = 'none';
        document.getElementById('controlsSection').style.display = 'none';
        
        document.getElementById('errorTitle').textContent = 'Error Loading Data';
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorState').style.display = 'flex';
    }

    displayResults(chain, stats, userData) {
        this.isLoading = false;
        this.updateButtonState();

        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('errorState').style.display = 'none';

        // Show user profile
        if (userData) {
            this.displayUserProfile(userData, stats);
        }

        // Show statistics
        if (stats) {
            this.displayStatistics(stats, chain.stats);
        }

        // Show controls
        document.getElementById('controlsSection').style.display = 'block';

        // Show tree
        this.displayTree(chain);
        document.getElementById('treeContainer').style.display = 'block';
    }

    displayStatistics(userStats, chainStats) {
        // Use actual database counts, not limited chain stats
        document.getElementById('totalReferrals').textContent = userStats.totalReferrals || 0;
        document.getElementById('maxDepth').textContent = chainStats.maxDepth || 0;
        // Show actual referral earnings, not user balance
        document.getElementById('totalEarnings').textContent = `₹${userStats.totalEarnings || 0}`;
        document.getElementById('activeReferrals').textContent = userStats.activeReferrals || 0;

        document.getElementById('statsSection').style.display = 'block';
    }

    displayTree(chain) {
        const treeVisualization = document.getElementById('treeVisualization');
        const treeTitle = document.getElementById('treeTitle');
        const treeStats = document.getElementById('treeStats');

        treeTitle.textContent = `Referral Chain for ${chain.user.first_name} (${chain.user.user_id})`;

        // Update stats display with filter info
        let statsText = `${chain.stats.totalReferrals} total referrals, ${chain.stats.maxDepth} levels deep`;
        if (this.currentReferralFilter) {
            statsText += ` (filtered by ${this.currentReferralFilter})`;
        }
        treeStats.textContent = statsText;

        // Add filter indicator if active
        if (this.currentReferralFilter) {
            const filterIndicator = document.createElement('div');
            filterIndicator.className = 'tree-filter-indicator';
            filterIndicator.innerHTML = `
                <i class="fas fa-filter"></i>
                <span>Tree view with referral filter: ${this.currentReferralFilter}</span>
                <button onclick="visualizer.clearReferralDateFilter()" class="filter-clear-btn-small">
                    <i class="fas fa-times"></i> Clear
                </button>
            `;
            treeVisualization.appendChild(filterIndicator);
        }

        this.renderNode(chain, treeVisualization, true);
    }

    renderNode(node, container, isRoot = false) {
        const nodeElement = document.createElement('div');
        nodeElement.className = `tree-node ${isRoot ? 'root' : ''}`;
        nodeElement.dataset.userId = node.user.user_id;

        const user = node.user;
        const hasChildren = node.children && node.children.length > 0;
        const isExpanded = this.expandedNodes.has(user.user_id.toString());

        // Node content
        const nodeContent = document.createElement('div');
        nodeContent.className = 'node-content';
        
        if (user.banned) {
            nodeContent.classList.add('banned');
        }
        
        if (user.custom_referral_info) {
            nodeContent.classList.add('custom-referral');
        }

        nodeContent.innerHTML = `
            <div class="node-info">
                <div class="node-name">
                    ${user.first_name || 'Unknown'} ${user.last_name || ''}
                    ${user.banned ? '🚫' : ''}
                    ${user.custom_referral_info ? '🔗' : ''}
                </div>
                <div class="node-details">
                    <span>ID: ${user.user_id}</span>
                    <span>@${user.username || 'No username'}</span>
                    <span>Balance: ₹${user.balance || 0}</span>
                    ${hasChildren ? `<span>Referrals: ${node.totalDirectReferrals || node.children.length}</span>` : ''}
                </div>
            </div>
            <div class="node-actions">
                ${hasChildren ? `
                    <button class="expand-btn ${isExpanded ? 'expanded' : ''}" 
                            onclick="visualizer.toggleNode('${user.user_id}')">
                        <i class="fas fa-${isExpanded ? 'minus' : 'plus'}"></i>
                    </button>
                ` : ''}
                <button class="info-btn" onclick="visualizer.showUserDetails('${user.user_id}')">
                    <i class="fas fa-info"></i>
                </button>
            </div>
        `;

        nodeElement.appendChild(nodeContent);

        // Children container
        if (hasChildren) {
            const childrenContainer = document.createElement('div');
            childrenContainer.className = 'node-children';
            childrenContainer.style.display = isExpanded ? 'block' : 'none';

            node.children.forEach(child => {
                this.renderNode(child, childrenContainer);
            });

            // Show "Load More" button if there are more referrals
            if (node.hasMore) {
                const loadMoreBtn = document.createElement('button');
                loadMoreBtn.className = 'load-more-btn';
                loadMoreBtn.innerHTML = '<i class="fas fa-plus"></i> Load More Referrals';
                loadMoreBtn.onclick = () => this.loadMoreReferrals(user.user_id, childrenContainer);
                childrenContainer.appendChild(loadMoreBtn);
            }

            nodeElement.appendChild(childrenContainer);
        }

        container.appendChild(nodeElement);
    }

    toggleNode(userId) {
        const userIdStr = userId.toString();
        const nodeElement = document.querySelector(`[data-user-id="${userId}"]`);
        const childrenContainer = nodeElement.querySelector('.node-children');
        const expandBtn = nodeElement.querySelector('.expand-btn');
        
        if (this.expandedNodes.has(userIdStr)) {
            this.expandedNodes.delete(userIdStr);
            childrenContainer.style.display = 'none';
            expandBtn.classList.remove('expanded');
            expandBtn.innerHTML = '<i class="fas fa-plus"></i>';
        } else {
            this.expandedNodes.add(userIdStr);
            childrenContainer.style.display = 'block';
            expandBtn.classList.add('expanded');
            expandBtn.innerHTML = '<i class="fas fa-minus"></i>';
        }
    }

    async loadMoreReferrals(userId, container) {
        try {
            const includeCustomReferrals = document.getElementById('includeCustomReferrals').checked;
            const showBannedUsers = document.getElementById('showBannedUsers').checked;
            
            // Calculate current page (count existing children)
            const existingChildren = container.querySelectorAll('.tree-node').length;
            const page = Math.floor(existingChildren / 50) + 2; // Next page
            
            const response = await fetch(`/api/referrals/${userId}?` + new URLSearchParams({
                page: page.toString(),
                limit: '50',
                includeBanned: showBannedUsers.toString(),
                includeCustomReferrals: includeCustomReferrals.toString()
            }));

            if (!response.ok) {
                throw new Error('Failed to load more referrals');
            }

            const data = await response.json();
            
            // Remove the "Load More" button
            const loadMoreBtn = container.querySelector('.load-more-btn');
            if (loadMoreBtn) {
                loadMoreBtn.remove();
            }

            // Add new referrals
            data.referrals.forEach(referral => {
                const childNode = {
                    user: referral,
                    children: [],
                    depth: 1
                };
                this.renderNode(childNode, container);
            });

            // Add new "Load More" button if there are more referrals
            if (data.hasMore) {
                const newLoadMoreBtn = document.createElement('button');
                newLoadMoreBtn.className = 'load-more-btn';
                newLoadMoreBtn.innerHTML = '<i class="fas fa-plus"></i> Load More Referrals';
                newLoadMoreBtn.onclick = () => this.loadMoreReferrals(userId, container);
                container.appendChild(newLoadMoreBtn);
            }

        } catch (error) {
            console.error('Error loading more referrals:', error);
            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'loading-more';
            errorDiv.innerHTML = `<span style="color: #e53e3e;">Error loading more referrals: ${error.message}</span>`;
            container.appendChild(errorDiv);
        }
    }

    updateButtonState() {
        const searchBtn = document.getElementById('searchBtn');
        const btnText = searchBtn.querySelector('.btn-text');
        const btnLoading = searchBtn.querySelector('.btn-loading');

        if (this.isLoading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline';
            searchBtn.disabled = true;
        } else {
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            searchBtn.disabled = false;
        }
    }

    expandAll() {
        const allNodes = document.querySelectorAll('.tree-node[data-user-id]');
        allNodes.forEach(node => {
            const userId = node.dataset.userId;
            const childrenContainer = node.querySelector('.node-children');
            const expandBtn = node.querySelector('.expand-btn');

            if (childrenContainer && expandBtn) {
                this.expandedNodes.add(userId);
                childrenContainer.style.display = 'block';
                expandBtn.classList.add('expanded');
                expandBtn.innerHTML = '<i class="fas fa-minus"></i>';
            }
        });
    }

    collapseAll() {
        const allNodes = document.querySelectorAll('.tree-node[data-user-id]');
        allNodes.forEach(node => {
            const userId = node.dataset.userId;
            const childrenContainer = node.querySelector('.node-children');
            const expandBtn = node.querySelector('.expand-btn');

            if (childrenContainer && expandBtn) {
                this.expandedNodes.delete(userId);
                childrenContainer.style.display = 'none';
                expandBtn.classList.remove('expanded');
                expandBtn.innerHTML = '<i class="fas fa-plus"></i>';
            }
        });
    }

    async searchInTree(searchTerm) {
        const clearSearchBtn = document.getElementById('clearSearchBtn');

        if (!searchTerm.trim()) {
            this.clearTreeSearch();
            return;
        }

        if (!this.currentUserId) {
            return;
        }

        clearSearchBtn.style.display = 'block';

        try {
            const response = await fetch(`/api/search/${this.currentUserId}?` + new URLSearchParams({
                q: searchTerm,
                maxDepth: '10',
                includeBanned: document.getElementById('showBannedUsers').checked.toString()
            }));

            if (response.ok) {
                const results = await response.json();
                this.highlightSearchResults(results);
            }
        } catch (error) {
            console.error('Error searching in tree:', error);
        }
    }

    highlightSearchResults(results) {
        // Clear previous highlights
        document.querySelectorAll('.tree-node.highlighted').forEach(node => {
            node.classList.remove('highlighted');
        });

        // Highlight matching nodes
        results.forEach(user => {
            const nodeElement = document.querySelector(`[data-user-id="${user.user_id}"]`);
            if (nodeElement) {
                nodeElement.classList.add('highlighted');

                // Expand path to this node
                let currentNode = nodeElement;
                while (currentNode) {
                    const parentNode = currentNode.closest('.node-children')?.parentElement;
                    if (parentNode && parentNode.dataset.userId) {
                        this.expandedNodes.add(parentNode.dataset.userId);
                        const childrenContainer = parentNode.querySelector('.node-children');
                        const expandBtn = parentNode.querySelector('.expand-btn');
                        if (childrenContainer && expandBtn) {
                            childrenContainer.style.display = 'block';
                            expandBtn.classList.add('expanded');
                            expandBtn.innerHTML = '<i class="fas fa-minus"></i>';
                        }
                    }
                    currentNode = parentNode;
                }
            }
        });

        // Scroll to first result
        if (results.length > 0) {
            const firstResult = document.querySelector(`[data-user-id="${results[0].user_id}"]`);
            if (firstResult) {
                firstResult.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    }

    clearTreeSearch() {
        document.getElementById('treeSearchInput').value = '';
        document.getElementById('clearSearchBtn').style.display = 'none';

        // Clear highlights
        document.querySelectorAll('.tree-node.highlighted').forEach(node => {
            node.classList.remove('highlighted');
        });
    }

    async showUserDetails(userId) {
        try {
            const response = await fetch(`/api/user/${userId}`);
            if (!response.ok) {
                throw new Error('Failed to fetch user details');
            }

            const user = await response.json();
            this.displayUserModal(user);
        } catch (error) {
            console.error('Error fetching user details:', error);
            alert('Failed to load user details');
        }
    }

    displayUserModal(user) {
        const modalBody = document.getElementById('modalBody');

        const joinedDate = user.created_at ? new Date(user.created_at * 1000).toLocaleDateString() : 'Unknown';
        const promotionReports = user.promotion_report || [];
        const totalEarnings = promotionReports.reduce((sum, report) => sum + (report.amount_got || 0), 0);

        modalBody.innerHTML = `
            <div class="user-detail-grid">
                <div class="user-detail-item">
                    <div class="user-detail-label">User ID</div>
                    <div class="user-detail-value">${user.user_id}</div>
                </div>
                <div class="user-detail-item">
                    <div class="user-detail-label">Name</div>
                    <div class="user-detail-value">${user.first_name || ''} ${user.last_name || ''}</div>
                </div>
                <div class="user-detail-item">
                    <div class="user-detail-label">Username</div>
                    <div class="user-detail-value">@${user.username || 'No username'}</div>
                </div>
                <div class="user-detail-item">
                    <div class="user-detail-label">Balance</div>
                    <div class="user-detail-value">₹${user.balance || 0}</div>
                </div>
                <div class="user-detail-item">
                    <div class="user-detail-label">Status</div>
                    <div class="user-detail-value">${user.banned ? '🚫 Banned' : '✅ Active'}</div>
                </div>
                <div class="user-detail-item">
                    <div class="user-detail-label">Joined</div>
                    <div class="user-detail-value">${joinedDate}</div>
                </div>
                <div class="user-detail-item">
                    <div class="user-detail-label">Referred By</div>
                    <div class="user-detail-value">${user.referred_by === 'None' ? 'Direct join' : user.referred_by}</div>
                </div>
                <div class="user-detail-item">
                    <div class="user-detail-label">Referral Earnings</div>
                    <div class="user-detail-value">₹${totalEarnings}</div>
                </div>
                <div class="user-detail-item">
                    <div class="user-detail-label">Total Referrals</div>
                    <div class="user-detail-value">${promotionReports.length}</div>
                </div>
                <div class="user-detail-item">
                    <div class="user-detail-label">Withdrawals</div>
                    <div class="user-detail-value">₹${user.successful_withdraw || 0}</div>
                </div>
            </div>

            ${user.custom_referral_info ? `
                <div style="margin-top: 20px; padding: 15px; background: #e6fffa; border-radius: 10px; border: 1px solid #81e6d9;">
                    <h4 style="margin-bottom: 10px; color: #2d3748;">Custom Referral Info</h4>
                    <p><strong>Parameter:</strong> ${user.custom_referral_info.parameter}</p>
                    <p><strong>Created by User:</strong> ${user.custom_referral_info.created_by}</p>
                </div>
            ` : ''}

            ${user.account_info && Object.values(user.account_info).some(v => v) ? `
                <div style="margin-top: 20px; padding: 15px; background: #f7fafc; border-radius: 10px; border: 1px solid #e2e8f0;">
                    <h4 style="margin-bottom: 10px; color: #2d3748;">Account Information</h4>
                    ${user.account_info.name ? `<p><strong>Name:</strong> ${user.account_info.name}</p>` : ''}
                    ${user.account_info.email ? `<p><strong>Email:</strong> ${user.account_info.email}</p>` : ''}
                    ${user.account_info.mobile_number ? `<p><strong>Mobile:</strong> ${user.account_info.mobile_number}</p>` : ''}
                    ${user.account_info.withdrawal_method ? `<p><strong>Withdrawal Method:</strong> ${user.account_info.withdrawal_method}</p>` : ''}
                </div>
            ` : ''}
        `;

        document.getElementById('userModal').style.display = 'flex';
    }

    closeModal() {
        document.getElementById('userModal').style.display = 'none';
    }

    async exportData() {
        if (!this.currentUserId || !this.currentChain) {
            alert('No data to export. Please search for a user first.');
            return;
        }

        try {
            const includeCustomReferrals = document.getElementById('includeCustomReferrals').checked;
            const showBannedUsers = document.getElementById('showBannedUsers').checked;

            const response = await fetch(`/api/export/${this.currentUserId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    format: 'json',
                    maxDepth: 10,
                    includeBanned: showBannedUsers,
                    includeCustomReferrals: includeCustomReferrals
                })
            });

            if (!response.ok) {
                throw new Error('Failed to export data');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `referral-chain-${this.currentUserId}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

        } catch (error) {
            console.error('Error exporting data:', error);
            alert('Failed to export data');
        }
    }

    async toggleDatabaseStats() {
        const statsSection = document.getElementById('databaseStatsSection');
        const showStatsBtn = document.getElementById('showStatsBtn');

        if (statsSection.style.display === 'none' || !statsSection.style.display) {
            await this.loadDatabaseStats();
            statsSection.style.display = 'block';
            showStatsBtn.innerHTML = '<i class="fas fa-chart-bar"></i> Hide Stats';
        } else {
            statsSection.style.display = 'none';
            showStatsBtn.innerHTML = '<i class="fas fa-chart-bar"></i> Database Stats';
        }
    }

    async loadDatabaseStats() {
        try {
            const response = await fetch('/api/database-stats');
            if (!response.ok) {
                throw new Error('Failed to fetch database stats');
            }

            const stats = await response.json();

            document.getElementById('dbTotalUsers').textContent = stats.totalUsers.toLocaleString();
            document.getElementById('dbActiveUsers').textContent = stats.activeUsers.toLocaleString();
            document.getElementById('dbReferredUsers').textContent = stats.referredUsers.toLocaleString();
            document.getElementById('dbCustomReferrals').textContent = stats.customReferrals.toLocaleString();

        } catch (error) {
            console.error('Error loading database stats:', error);
            document.getElementById('dbTotalUsers').textContent = 'Error';
            document.getElementById('dbActiveUsers').textContent = 'Error';
            document.getElementById('dbReferredUsers').textContent = 'Error';
            document.getElementById('dbCustomReferrals').textContent = 'Error';
        }
    }

    async performHealthCheck() {
        try {
            const response = await fetch('/api/health');
            const health = await response.json();

            if (health.status === 'OK') {
                alert(`✅ System Health: OK\n\n` +
                      `Database: ${health.database}\n` +
                      `Users: ${health.userCount?.toLocaleString() || 'N/A'}\n` +
                      `Version: ${health.version}\n` +
                      `Timestamp: ${new Date(health.timestamp).toLocaleString()}`);
            } else {
                alert(`❌ System Health: ${health.status}\n\n` +
                      `Message: ${health.message}\n` +
                      `Timestamp: ${new Date(health.timestamp).toLocaleString()}`);
            }
        } catch (error) {
            console.error('Health check failed:', error);
            alert('❌ Health check failed: Unable to connect to server');
        }
    }

    displayUserProfile(userData, stats) {
        // Store current user data
        this.currentUserData = userData;

        // Update profile header
        const fullName = `${userData.first_name || ''} ${userData.last_name || ''}`.trim();
        document.getElementById('profileName').textContent = fullName || 'Unknown User';
        document.getElementById('profileUsername').textContent = `@${userData.username || 'no_username'}`;

        const statusBadge = document.getElementById('profileStatus');
        if (userData.banned) {
            statusBadge.textContent = 'Banned';
            statusBadge.className = 'status-badge banned';
        } else {
            statusBadge.textContent = 'Active';
            statusBadge.className = 'status-badge';
        }

        // Update overview tab
        this.updateOverviewTab(userData);

        // Update financial tab
        this.updateFinancialTab(userData, stats);

        // Update transactions tab
        this.updateTransactionsTab(userData, stats);

        // Update account tab
        this.updateAccountTab(userData);

        // Update referrals tab
        this.updateReferralsTab(userData);

        // Show the profile section
        document.getElementById('userProfileSection').style.display = 'block';
    }

    updateOverviewTab(userData) {
        // Member since
        if (userData.created_at) {
            const joinDate = new Date(userData.created_at * 1000);
            document.getElementById('memberSince').textContent = joinDate.toLocaleDateString();

            // Account age
            const now = new Date();
            const diffTime = Math.abs(now - joinDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            document.getElementById('accountAge').textContent = `${diffDays} days`;
        } else {
            document.getElementById('memberSince').textContent = 'Unknown';
            document.getElementById('accountAge').textContent = 'Unknown';
        }

        // Referred by
        const referredBy = userData.referred_by;
        if (referredBy === 'None') {
            document.getElementById('referredBy').textContent = 'Direct join';
        } else if (referredBy && !isNaN(referredBy)) {
            document.getElementById('referredBy').innerHTML = `<a href="#" onclick="visualizer.searchReferralChain('${referredBy}')">${referredBy}</a>`;
        } else {
            document.getElementById('referredBy').textContent = referredBy || 'Unknown';
        }

        // User ID
        document.getElementById('userIdDisplay').textContent = userData.user_id;
    }

    updateFinancialTab(userData, stats) {
        // Current balance
        document.getElementById('currentBalance').textContent = `₹${userData.balance || 0}`;

        // Referral earnings
        document.getElementById('referralEarnings').textContent = `₹${stats?.totalEarnings || 0}`;

        // Total withdrawals
        document.getElementById('totalWithdrawals').textContent = `₹${userData.successful_withdraw || 0}`;

        // Net earnings (balance + withdrawals)
        const netEarnings = (userData.balance || 0) + (userData.successful_withdraw || 0);
        document.getElementById('netEarnings').textContent = `₹${netEarnings}`;

        // Today's activity
        if (stats?.todayEarnings) {
            document.getElementById('todayEarnings').textContent = `₹${stats.todayEarnings.amount || 0}`;
            document.getElementById('todayReferrals').textContent = stats.todayEarnings.referrals || 0;
        }

        // Daily earnings chart
        this.updateDailyEarningsChart(stats?.dailyEarnings || []);
    }

    updateAccountTab(userData) {
        const accountInfo = userData.account_info || {};

        // Personal information
        document.getElementById('fullName').textContent = accountInfo.name || 'Not provided';
        document.getElementById('email').textContent = accountInfo.email || 'Not provided';
        document.getElementById('mobile').textContent = accountInfo.mobile_number || 'Not provided';

        // Banking information
        document.getElementById('accountNumber').textContent = accountInfo.account_number || 'Not provided';
        document.getElementById('ifscCode').textContent = accountInfo.ifsc || 'Not provided';
        document.getElementById('withdrawalMethod').textContent = accountInfo.withdrawal_method || 'Not set';

        // Crypto information
        document.getElementById('usdtAddress').textContent = accountInfo.usdt_address || 'Not provided';
        document.getElementById('binanceId').textContent = accountInfo.binance_id || 'Not provided';
    }

    updateReferralsTab(userData) {
        const promotionReport = userData.promotion_report || [];

        // Store original data for filtering
        this.originalReferralData = promotionReport;

        // Clear any existing filter
        this.currentReferralFilter = null;
        document.getElementById('referralDateFilter').value = '';
        document.getElementById('clearReferralFilter').style.display = 'none';
        document.getElementById('referralFilterStatus').style.display = 'none';

        // Display referrals
        this.displayReferrals(promotionReport, false);
    }

    displayReferrals(referrals, isFiltered = false) {
        const topReferralsList = document.getElementById('topReferralsList');

        // Update count display
        if (isFiltered && this.originalReferralData) {
            document.getElementById('totalReferralsCount').textContent =
                `${referrals.length} of ${this.originalReferralData.length} referrals`;
        } else {
            document.getElementById('totalReferralsCount').textContent = `${referrals.length} referrals`;
        }

        if (referrals.length === 0) {
            if (isFiltered) {
                topReferralsList.innerHTML = '<p style="text-align: center; color: #718096; padding: 20px;">No referrals found for the selected date</p>';
            } else {
                topReferralsList.innerHTML = '<p style="text-align: center; color: #718096; padding: 20px;">No referrals yet</p>';
            }
            return;
        }

        // Sort by amount (highest first) and display all filtered results or top 20 for unfiltered
        const displayReferrals = referrals
            .sort((a, b) => (b.amount_got || 0) - (a.amount_got || 0))
            .slice(0, isFiltered ? referrals.length : 20);

        topReferralsList.innerHTML = displayReferrals.map(referral => {
            const initials = (referral.referred_user_name || 'U').substring(0, 2).toUpperCase();
            const joinDate = referral.referred_user_join_date ?
                new Date(referral.referred_user_join_date * 1000).toLocaleDateString() : 'Unknown';

            return `
                <div class="referral-item">
                    <div class="referral-info">
                        <div class="referral-avatar">${initials}</div>
                        <div class="referral-details">
                            <h5>${referral.referred_user_name || 'Unknown User'}</h5>
                            <p>ID: ${referral.referred_user_id || 'Unknown'}</p>
                            <p style="font-size: 0.8rem; color: #a0aec0;">Joined: ${joinDate}</p>
                        </div>
                    </div>
                    <div class="referral-earnings">
                        <div class="amount">₹${referral.amount_got || 0}</div>
                        <button class="action-btn-small" onclick="visualizer.searchReferralChain('${referral.referred_user_id}')">
                            View Chain
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    updateTransactionsTab(userData, stats) {
        // Update withdrawal history
        this.updateWithdrawalHistory(stats?.withdrawalHistory || []);

        // Update gift code history
        this.updateGiftCodeHistory(stats?.giftCodeHistory || []);

        // Update custom referral links
        this.updateCustomReferralLinks(stats?.customReferralLinks || []);
    }

    updateWithdrawalHistory(withdrawals) {
        const withdrawalHistory = document.getElementById('withdrawalHistory');
        const withdrawalCount = document.getElementById('withdrawalCount');

        withdrawalCount.textContent = `${withdrawals.length} withdrawals`;

        if (withdrawals.length === 0) {
            withdrawalHistory.innerHTML = `
                <div class="empty-transactions">
                    <i class="fas fa-money-bill-wave"></i>
                    <p>No withdrawal history found</p>
                </div>
            `;
            return;
        }

        withdrawalHistory.innerHTML = withdrawals.map(withdrawal => `
            <div class="transaction-item">
                <div class="transaction-info">
                    <div class="transaction-title">Withdrawal Request</div>
                    <div class="transaction-details">
                        Method: ${withdrawal.method || 'Bank'} •
                        ${withdrawal.date || 'Date not available'}
                    </div>
                </div>
                <div class="transaction-amount">
                    <div class="amount">₹${withdrawal.amount || 0}</div>
                    <div class="status ${(withdrawal.status || 'pending').toLowerCase()}">${withdrawal.status || 'Pending'}</div>
                </div>
            </div>
        `).join('');
    }

    updateGiftCodeHistory(giftCodes) {
        const giftCodeHistory = document.getElementById('giftCodeHistory');
        const giftCodeCount = document.getElementById('giftCodeCount');

        giftCodeCount.textContent = `${giftCodes.length} codes`;

        if (giftCodes.length === 0) {
            giftCodeHistory.innerHTML = `
                <div class="empty-transactions">
                    <i class="fas fa-gift"></i>
                    <p>No gift codes redeemed</p>
                </div>
            `;
            return;
        }

        giftCodeHistory.innerHTML = giftCodes.map(code => {
            const date = code.redeemed_at ? new Date(code.redeemed_at * 1000).toLocaleDateString() : 'Unknown date';
            return `
                <div class="transaction-item">
                    <div class="transaction-info">
                        <div class="transaction-title">Gift Code: ${code.code}</div>
                        <div class="transaction-details">
                            Type: ${code.type === 'link_code' ? 'Link-based' : 'Traditional'} •
                            ${date}
                        </div>
                    </div>
                    <div class="transaction-amount">
                        <div class="amount">₹${code.amount || 0}</div>
                        <div class="status completed">Redeemed</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    updateCustomReferralLinks(customLinks) {
        const customReferralLinks = document.getElementById('customReferralLinks');
        const customLinksCount = document.getElementById('customLinksCount');

        customLinksCount.textContent = `${customLinks.length} links`;

        if (customLinks.length === 0) {
            customReferralLinks.innerHTML = `
                <div class="empty-transactions">
                    <i class="fas fa-link"></i>
                    <p>No custom referral links created</p>
                </div>
            `;
            return;
        }

        customReferralLinks.innerHTML = customLinks.map(link => {
            const date = link.created_at ? new Date(link.created_at * 1000).toLocaleDateString() : 'Unknown date';
            return `
                <div class="transaction-item">
                    <div class="transaction-info">
                        <div class="transaction-title">Custom Link: ${link.parameter}</div>
                        <div class="transaction-details">
                            Created: ${date} •
                            Uses: ${link.uses || 0}
                        </div>
                    </div>
                    <div class="transaction-amount">
                        <div class="status completed">Active</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    updateDailyEarningsChart(dailyEarnings) {
        const chartContainer = document.getElementById('dailyEarningsChart');

        if (!dailyEarnings || dailyEarnings.length === 0) {
            chartContainer.innerHTML = '<p>No daily earnings data available</p>';
            return;
        }

        // Take last 7 days
        const last7Days = dailyEarnings.slice(0, 7).reverse();

        // Create simple bar chart
        const maxEarnings = Math.max(...last7Days.map(d => d.earnings));

        chartContainer.innerHTML = `
            <div style="display: flex; align-items: end; gap: 10px; height: 150px; padding: 20px;">
                ${last7Days.map(day => {
                    const height = maxEarnings > 0 ? (day.earnings / maxEarnings) * 100 : 0;
                    const date = new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                    return `
                        <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                            <div style="background: linear-gradient(135deg, #667eea, #764ba2); width: 100%; height: ${height}%; min-height: 5px; border-radius: 4px; margin-bottom: 10px; position: relative;">
                                <div style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-size: 0.8rem; color: #2d3748; font-weight: 600;">₹${day.earnings}</div>
                            </div>
                            <div style="font-size: 0.75rem; color: #718096; text-align: center;">${date}</div>
                            <div style="font-size: 0.7rem; color: #a0aec0;">${day.referrals} ref</div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');
    }

    async refreshUserProfile() {
        if (!this.currentUserId) return;

        try {
            const userResponse = await fetch(`/api/user/${this.currentUserId}`);
            const userData = await userResponse.json();

            const statsResponse = await fetch(`/api/stats/${this.currentUserId}`);
            const stats = await statsResponse.json();

            this.displayUserProfile(userData, stats);
        } catch (error) {
            console.error('Error refreshing user profile:', error);
            alert('Failed to refresh user profile');
        }
    }

    async exportUserData() {
        if (!this.currentUserData) {
            alert('No user data to export');
            return;
        }

        try {
            const dataToExport = {
                user: this.currentUserData,
                exportedAt: new Date().toISOString(),
                exportedBy: 'Referral Chain Visualizer'
            };

            const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `user-${this.currentUserData.user_id}-profile.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('Error exporting user data:', error);
            alert('Failed to export user data');
        }
    }

    // Date Analysis Methods
    initializeDateAnalysisListeners() {
        // Filter tab switching
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchFilterTab(e.target.dataset.filter));
        });

        // Single date analysis
        document.getElementById('analyzeSingleDate').addEventListener('click', () => this.analyzeSingleDate());

        // Date range analysis
        document.getElementById('analyzeDateRange').addEventListener('click', () => this.analyzeDateRange());

        // Preset analysis
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.analyzePreset(e.target.dataset.preset));
        });

        // Sorting
        document.getElementById('applySorting').addEventListener('click', () => this.applySorting());

        // Export functions
        document.getElementById('exportCSV').addEventListener('click', () => this.exportResults('csv'));
        document.getElementById('exportJSON').addEventListener('click', () => this.exportResults('json'));

        // Pagination
        document.getElementById('prevPage').addEventListener('click', () => this.changePage(-1));
        document.getElementById('nextPage').addEventListener('click', () => this.changePage(1));

        // Set default date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('singleDate').value = today;
        document.getElementById('endDate').value = today;

        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 7);
        document.getElementById('startDate').value = yesterday.toISOString().split('T')[0];
    }

    toggleDateAnalysis() {
        const section = document.getElementById('dateAnalysisSection');
        const isVisible = section.style.display !== 'none';

        // Hide other sections
        document.getElementById('userProfileSection').style.display = 'none';
        document.getElementById('statsSection').style.display = 'none';
        document.getElementById('controlsSection').style.display = 'none';
        document.getElementById('treeContainer').style.display = 'none';

        if (isVisible) {
            section.style.display = 'none';
        } else {
            section.style.display = 'block';
            // Reset to empty state
            document.getElementById('analysisResults').style.display = 'none';
            document.getElementById('analysisLoading').style.display = 'none';
            document.getElementById('analysisEmpty').style.display = 'none';
        }
    }

    switchFilterTab(filterType) {
        // Update tab buttons
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

        // Update filter content
        document.querySelectorAll('.filter-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${filterType}${filterType === 'single' ? 'Date' : filterType === 'range' ? 'Range' : ''}Filter`).classList.add('active');
    }

    async analyzeSingleDate() {
        const date = document.getElementById('singleDate').value;
        if (!date) {
            alert('Please select a date');
            return;
        }

        await this.performAnalysis('date', date);
    }

    async analyzeDateRange() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        if (!startDate || !endDate) {
            alert('Please select both start and end dates');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            alert('Start date must be before end date');
            return;
        }

        await this.performAnalysis('range', `${startDate}/${endDate}`);
    }

    async analyzePreset(preset) {
        this.lastUsedPreset = preset;
        await this.performAnalysis('preset', preset);
    }

    async performAnalysis(type, value) {
        this.showAnalysisLoading();
        this.currentAnalysisData = null;
        this.currentPage = 1;

        try {
            let url;
            const sortBy = document.getElementById('sortBy').value;
            const sortOrder = document.getElementById('sortOrder').value;
            const params = new URLSearchParams({
                sortBy,
                sortOrder,
                page: this.currentPage,
                limit: 50
            });

            switch (type) {
                case 'date':
                    url = `/api/analysis/date/${value}?${params}`;
                    break;
                case 'range':
                    url = `/api/analysis/range/${value}?${params}`;
                    break;
                case 'preset':
                    url = `/api/analysis/preset/${value}?${params}`;
                    break;
                default:
                    throw new Error('Invalid analysis type');
            }

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error('Failed to fetch analysis data');
            }

            const data = await response.json();
            this.currentAnalysisData = data;
            this.displayAnalysisResults(data);

        } catch (error) {
            console.error('Analysis error:', error);
            this.showAnalysisError(error.message);
        }
    }

    showAnalysisLoading() {
        document.getElementById('analysisResults').style.display = 'none';
        document.getElementById('analysisEmpty').style.display = 'none';
        document.getElementById('analysisLoading').style.display = 'block';
    }

    showAnalysisError(message) {
        document.getElementById('analysisLoading').style.display = 'none';
        document.getElementById('analysisResults').style.display = 'none';
        document.getElementById('analysisEmpty').style.display = 'block';

        const emptySection = document.getElementById('analysisEmpty');
        emptySection.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Analysis Error</h3>
            <p>${message}</p>
        `;
    }

    displayAnalysisResults(data) {
        document.getElementById('analysisLoading').style.display = 'none';

        if (data.users.length === 0) {
            document.getElementById('analysisEmpty').style.display = 'block';
            document.getElementById('analysisResults').style.display = 'none';
            return;
        }

        document.getElementById('analysisEmpty').style.display = 'none';
        document.getElementById('analysisResults').style.display = 'block';

        // Update summary statistics
        this.updateSummaryStatistics(data.summary);

        // Update results table
        this.updateResultsTable(data.users);

        // Update pagination
        this.updatePagination(data.pagination);
    }

    updateSummaryStatistics(summary) {
        document.getElementById('summaryTotalUsers').textContent = summary.totalUsers;
        document.getElementById('summaryTotalEarnings').textContent = `₹${summary.totalEarnings}`;
        document.getElementById('summaryTotalReferrals').textContent = summary.totalReferrals;

        const topReferrer = summary.topReferrer;
        if (topReferrer) {
            document.getElementById('summaryTopReferrer').textContent =
                `${topReferrer.name} (₹${topReferrer.earnings})`;
        } else {
            document.getElementById('summaryTopReferrer').textContent = 'No referrals';
        }
    }

    updateResultsTable(users) {
        const tbody = document.getElementById('resultsTableBody');

        tbody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <div class="user-info">
                        <div class="user-name">${user.name}</div>
                        <div class="user-details">@${user.username} • ID: ${user.user_id}</div>
                    </div>
                </td>
                <td>
                    <div>${user.joinedDate}</div>
                    <div style="font-size: 0.8rem; color: #718096;">${user.joinedTime}</div>
                </td>
                <td class="referrals-cell">${user.referralsInRange}</td>
                <td class="earnings-cell">₹${user.earningsInRange}</td>
                <td class="earnings-cell">₹${user.totalEarnings}</td>
                <td>
                    ${user.referrer ? `
                        <div class="referrer-info">
                            <div class="referrer-name">${user.referrer.name}</div>
                            <div class="referrer-id">@${user.referrer.username} • ${user.referrer.user_id}</div>
                        </div>
                    ` : '<span style="color: #718096;">Direct join</span>'}
                </td>
                <td>
                    <button class="action-btn-small" onclick="visualizer.searchReferralChain('${user.user_id}')">
                        View Chain
                    </button>
                </td>
            </tr>
        `).join('');
    }

    updatePagination(pagination) {
        document.getElementById('pageInfo').textContent =
            `Page ${pagination.page} of ${pagination.totalPages}`;

        document.getElementById('prevPage').disabled = pagination.page <= 1;
        document.getElementById('nextPage').disabled = pagination.page >= pagination.totalPages;
    }

    async applySorting() {
        if (!this.currentAnalysisData) {
            return;
        }

        // Re-run the current analysis with new sorting
        const currentType = this.getCurrentAnalysisType();
        const currentValue = this.getCurrentAnalysisValue();

        await this.performAnalysis(currentType, currentValue);
    }

    getCurrentAnalysisType() {
        // Determine current analysis type based on active tab
        const activeTab = document.querySelector('.filter-tab.active');
        return activeTab.dataset.filter;
    }

    getCurrentAnalysisValue() {
        const type = this.getCurrentAnalysisType();

        switch (type) {
            case 'single':
                return document.getElementById('singleDate').value;
            case 'range':
                return `${document.getElementById('startDate').value}/${document.getElementById('endDate').value}`;
            case 'preset':
                // Return the last used preset (we'll need to track this)
                return this.lastUsedPreset || 'today';
            default:
                return '';
        }
    }

    async changePage(direction) {
        if (!this.currentAnalysisData) {
            return;
        }

        const newPage = this.currentPage + direction;
        const totalPages = this.currentAnalysisData.pagination.totalPages;

        if (newPage < 1 || newPage > totalPages) {
            return;
        }

        this.currentPage = newPage;

        // Re-run analysis with new page
        const currentType = this.getCurrentAnalysisType();
        const currentValue = this.getCurrentAnalysisValue();

        await this.performAnalysis(currentType, currentValue);
    }

    exportResults(format) {
        if (!this.currentAnalysisData) {
            alert('No data to export');
            return;
        }

        const data = this.currentAnalysisData;
        const filename = `referral-analysis-${data.dateRange}-${new Date().toISOString().split('T')[0]}`;

        if (format === 'csv') {
            this.exportAsCSV(data, filename);
        } else if (format === 'json') {
            this.exportAsJSON(data, filename);
        }
    }

    exportAsCSV(data, filename) {
        const headers = [
            'User ID', 'Name', 'Username', 'Join Date', 'Join Time',
            'Referrals (Period)', 'Earnings (Period)', 'Total Earnings',
            'Referrer Name', 'Referrer ID'
        ];

        const rows = data.users.map(user => [
            user.user_id,
            user.name,
            user.username,
            user.joinedDate,
            user.joinedTime,
            user.referralsInRange,
            user.earningsInRange,
            user.totalEarnings,
            user.referrer ? user.referrer.name : 'Direct join',
            user.referrer ? user.referrer.user_id : ''
        ]);

        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        this.downloadFile(csvContent, `${filename}.csv`, 'text/csv');
    }

    exportAsJSON(data, filename) {
        const exportData = {
            summary: data.summary,
            users: data.users,
            dateRange: data.dateRange,
            exportedAt: new Date().toISOString(),
            exportedBy: 'Referral Chain Visualizer'
        };

        const jsonContent = JSON.stringify(exportData, null, 2);
        this.downloadFile(jsonContent, `${filename}.json`, 'application/json');
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    // Referral Filter Methods
    initializeReferralFilterListeners() {
        document.getElementById('applyReferralFilter').addEventListener('click', () => this.applyReferralDateFilter());
        document.getElementById('clearReferralFilter').addEventListener('click', () => this.clearReferralDateFilter());

        // Allow Enter key to apply filter
        document.getElementById('referralDateFilter').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyReferralDateFilter();
            }
        });
    }

    async applyReferralDateFilter() {
        const selectedDate = document.getElementById('referralDateFilter').value;

        if (!selectedDate) {
            alert('Please select a date to filter by');
            return;
        }

        if (!this.originalReferralData || this.originalReferralData.length === 0) {
            alert('No referral data available to filter');
            return;
        }

        try {
            // Get detailed referral data for the current user with join dates
            const userId = this.currentUserData.user_id;
            const response = await fetch(`/api/user/${userId}/referrals?date=${selectedDate}`);

            if (!response.ok) {
                // Fallback to client-side filtering if API endpoint doesn't exist
                this.filterReferralsClientSide(selectedDate);
                return;
            }

            const data = await response.json();
            this.currentReferralFilter = selectedDate;

            // Display filtered results
            this.displayReferrals(data.referrals, true);
            this.showFilterStatus(selectedDate, data.referrals.length);

        } catch (error) {
            console.error('Error filtering referrals:', error);
            // Fallback to client-side filtering
            this.filterReferralsClientSide(selectedDate);
        }
    }

    filterReferralsClientSide(selectedDate) {
        // Client-side filtering as fallback
        // Note: This won't have join dates, so we'll filter by referral report date if available
        const filteredReferrals = this.originalReferralData.filter(referral => {
            // If we have join date data, use it
            if (referral.referred_user_join_date) {
                const joinDate = new Date(referral.referred_user_join_date * 1000).toISOString().split('T')[0];
                return joinDate === selectedDate;
            }
            // Otherwise, we can't filter accurately
            return false;
        });

        this.currentReferralFilter = selectedDate;
        this.displayReferrals(filteredReferrals, true);
        this.showFilterStatus(selectedDate, filteredReferrals.length);
    }

    showFilterStatus(date, count) {
        const statusElement = document.getElementById('referralFilterStatus');
        const statusText = document.getElementById('filterStatusText');

        statusText.textContent = `Showing ${count} referrals for ${date}`;
        statusElement.style.display = 'flex';
        statusElement.className = 'filter-status active';

        document.getElementById('clearReferralFilter').style.display = 'inline-flex';
    }

    clearReferralDateFilter() {
        this.currentReferralFilter = null;
        document.getElementById('referralDateFilter').value = '';
        document.getElementById('clearReferralFilter').style.display = 'none';
        document.getElementById('referralFilterStatus').style.display = 'none';

        // Display all referrals
        if (this.originalReferralData) {
            this.displayReferrals(this.originalReferralData, false);
        }
    }
}

// Initialize the application
const visualizer = new ReferralChainVisualizer();

// Make visualizer globally available for onclick handlers
window.visualizer = visualizer;
